<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\GroupsMigration\Resource;

use Google\Service\GroupsMigration\Groups;

/**
 * The "archive" collection of methods.
 * Typical usage is:
 *  <code>
 *   $groupsmigrationService = new Google\Service\GroupsMigration(...);
 *   $archive = $groupsmigrationService->archive;
 *  </code>
 */
class Archive extends \Google\Service\Resource
{
  /**
   * Inserts a new mail into the archive of the Google group. (archive.insert)
   *
   * @param string $groupId The group ID
   * @param array $optParams Optional parameters.
   * @return Groups
   * @throws \Google\Service\Exception
   */
  public function insert($groupId, $optParams = [])
  {
    $params = ['groupId' => $groupId];
    $params = array_merge($params, $optParams);
    return $this->call('insert', [$params], Groups::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Archive::class, 'Google_Service_GroupsMigration_Resource_Archive');
