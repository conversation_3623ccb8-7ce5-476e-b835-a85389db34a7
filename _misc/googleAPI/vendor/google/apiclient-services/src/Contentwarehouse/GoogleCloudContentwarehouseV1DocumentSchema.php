<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Contentwarehouse;

class GoogleCloudContentwarehouseV1DocumentSchema extends \Google\Collection
{
  protected $collection_key = 'propertyDefinitions';
  /**
   * @var string
   */
  public $createTime;
  /**
   * @var string
   */
  public $description;
  /**
   * @var string
   */
  public $displayName;
  /**
   * @var bool
   */
  public $documentIsFolder;
  /**
   * @var string
   */
  public $name;
  protected $propertyDefinitionsType = GoogleCloudContentwarehouseV1PropertyDefinition::class;
  protected $propertyDefinitionsDataType = 'array';
  /**
   * @var string
   */
  public $updateTime;

  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param string
   */
  public function setDisplayName($displayName)
  {
    $this->displayName = $displayName;
  }
  /**
   * @return string
   */
  public function getDisplayName()
  {
    return $this->displayName;
  }
  /**
   * @param bool
   */
  public function setDocumentIsFolder($documentIsFolder)
  {
    $this->documentIsFolder = $documentIsFolder;
  }
  /**
   * @return bool
   */
  public function getDocumentIsFolder()
  {
    return $this->documentIsFolder;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param GoogleCloudContentwarehouseV1PropertyDefinition[]
   */
  public function setPropertyDefinitions($propertyDefinitions)
  {
    $this->propertyDefinitions = $propertyDefinitions;
  }
  /**
   * @return GoogleCloudContentwarehouseV1PropertyDefinition[]
   */
  public function getPropertyDefinitions()
  {
    return $this->propertyDefinitions;
  }
  /**
   * @param string
   */
  public function setUpdateTime($updateTime)
  {
    $this->updateTime = $updateTime;
  }
  /**
   * @return string
   */
  public function getUpdateTime()
  {
    return $this->updateTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudContentwarehouseV1DocumentSchema::class, 'Google_Service_Contentwarehouse_GoogleCloudContentwarehouseV1DocumentSchema');
