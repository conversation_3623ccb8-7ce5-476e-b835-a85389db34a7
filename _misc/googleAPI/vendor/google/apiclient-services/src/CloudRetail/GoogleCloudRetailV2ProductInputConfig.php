<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudRetail;

class GoogleCloudRetailV2ProductInputConfig extends \Google\Model
{
  protected $bigQuerySourceType = GoogleCloudRetailV2BigQuerySource::class;
  protected $bigQuerySourceDataType = '';
  protected $gcsSourceType = GoogleCloudRetailV2GcsSource::class;
  protected $gcsSourceDataType = '';
  protected $productInlineSourceType = GoogleCloudRetailV2ProductInlineSource::class;
  protected $productInlineSourceDataType = '';

  /**
   * @param GoogleCloudRetailV2BigQuerySource
   */
  public function setBigQuerySource(GoogleCloudRetailV2BigQuerySource $bigQuerySource)
  {
    $this->bigQuerySource = $bigQuerySource;
  }
  /**
   * @return GoogleCloudRetailV2BigQuerySource
   */
  public function getBigQuerySource()
  {
    return $this->bigQuerySource;
  }
  /**
   * @param GoogleCloudRetailV2GcsSource
   */
  public function setGcsSource(GoogleCloudRetailV2GcsSource $gcsSource)
  {
    $this->gcsSource = $gcsSource;
  }
  /**
   * @return GoogleCloudRetailV2GcsSource
   */
  public function getGcsSource()
  {
    return $this->gcsSource;
  }
  /**
   * @param GoogleCloudRetailV2ProductInlineSource
   */
  public function setProductInlineSource(GoogleCloudRetailV2ProductInlineSource $productInlineSource)
  {
    $this->productInlineSource = $productInlineSource;
  }
  /**
   * @return GoogleCloudRetailV2ProductInlineSource
   */
  public function getProductInlineSource()
  {
    return $this->productInlineSource;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudRetailV2ProductInputConfig::class, 'Google_Service_CloudRetail_GoogleCloudRetailV2ProductInputConfig');
