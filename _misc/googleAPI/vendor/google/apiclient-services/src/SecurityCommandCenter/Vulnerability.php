<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\SecurityCommandCenter;

class Vulnerability extends \Google\Model
{
  protected $cveType = Cve::class;
  protected $cveDataType = '';
  protected $fixedPackageType = Package::class;
  protected $fixedPackageDataType = '';
  protected $offendingPackageType = Package::class;
  protected $offendingPackageDataType = '';
  protected $securityBulletinType = SecurityBulletin::class;
  protected $securityBulletinDataType = '';

  /**
   * @param Cve
   */
  public function setCve(Cve $cve)
  {
    $this->cve = $cve;
  }
  /**
   * @return Cve
   */
  public function getCve()
  {
    return $this->cve;
  }
  /**
   * @param Package
   */
  public function setFixedPackage(Package $fixedPackage)
  {
    $this->fixedPackage = $fixedPackage;
  }
  /**
   * @return Package
   */
  public function getFixedPackage()
  {
    return $this->fixedPackage;
  }
  /**
   * @param Package
   */
  public function setOffendingPackage(Package $offendingPackage)
  {
    $this->offendingPackage = $offendingPackage;
  }
  /**
   * @return Package
   */
  public function getOffendingPackage()
  {
    return $this->offendingPackage;
  }
  /**
   * @param SecurityBulletin
   */
  public function setSecurityBulletin(SecurityBulletin $securityBulletin)
  {
    $this->securityBulletin = $securityBulletin;
  }
  /**
   * @return SecurityBulletin
   */
  public function getSecurityBulletin()
  {
    return $this->securityBulletin;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Vulnerability::class, 'Google_Service_SecurityCommandCenter_Vulnerability');
