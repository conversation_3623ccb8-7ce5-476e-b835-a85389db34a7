<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\SecurityCommandCenter;

class GoogleCloudSecuritycenterV2Vulnerability extends \Google\Model
{
  protected $cveType = GoogleCloudSecuritycenterV2Cve::class;
  protected $cveDataType = '';
  protected $fixedPackageType = GoogleCloudSecuritycenterV2Package::class;
  protected $fixedPackageDataType = '';
  protected $offendingPackageType = GoogleCloudSecuritycenterV2Package::class;
  protected $offendingPackageDataType = '';
  protected $securityBulletinType = GoogleCloudSecuritycenterV2SecurityBulletin::class;
  protected $securityBulletinDataType = '';

  /**
   * @param GoogleCloudSecuritycenterV2Cve
   */
  public function setCve(GoogleCloudSecuritycenterV2Cve $cve)
  {
    $this->cve = $cve;
  }
  /**
   * @return GoogleCloudSecuritycenterV2Cve
   */
  public function getCve()
  {
    return $this->cve;
  }
  /**
   * @param GoogleCloudSecuritycenterV2Package
   */
  public function setFixedPackage(GoogleCloudSecuritycenterV2Package $fixedPackage)
  {
    $this->fixedPackage = $fixedPackage;
  }
  /**
   * @return GoogleCloudSecuritycenterV2Package
   */
  public function getFixedPackage()
  {
    return $this->fixedPackage;
  }
  /**
   * @param GoogleCloudSecuritycenterV2Package
   */
  public function setOffendingPackage(GoogleCloudSecuritycenterV2Package $offendingPackage)
  {
    $this->offendingPackage = $offendingPackage;
  }
  /**
   * @return GoogleCloudSecuritycenterV2Package
   */
  public function getOffendingPackage()
  {
    return $this->offendingPackage;
  }
  /**
   * @param GoogleCloudSecuritycenterV2SecurityBulletin
   */
  public function setSecurityBulletin(GoogleCloudSecuritycenterV2SecurityBulletin $securityBulletin)
  {
    $this->securityBulletin = $securityBulletin;
  }
  /**
   * @return GoogleCloudSecuritycenterV2SecurityBulletin
   */
  public function getSecurityBulletin()
  {
    return $this->securityBulletin;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudSecuritycenterV2Vulnerability::class, 'Google_Service_SecurityCommandCenter_GoogleCloudSecuritycenterV2Vulnerability');
