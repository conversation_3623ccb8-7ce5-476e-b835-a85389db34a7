<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Bigquery;

class SnapshotDefinition extends \Google\Model
{
  protected $baseTableReferenceType = TableReference::class;
  protected $baseTableReferenceDataType = '';
  /**
   * @var string
   */
  public $snapshotTime;

  /**
   * @param TableReference
   */
  public function setBaseTableReference(TableReference $baseTableReference)
  {
    $this->baseTableReference = $baseTableReference;
  }
  /**
   * @return TableReference
   */
  public function getBaseTableReference()
  {
    return $this->baseTableReference;
  }
  /**
   * @param string
   */
  public function setSnapshotTime($snapshotTime)
  {
    $this->snapshotTime = $snapshotTime;
  }
  /**
   * @return string
   */
  public function getSnapshotTime()
  {
    return $this->snapshotTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(SnapshotDefinition::class, 'Google_Service_Bigquery_SnapshotDefinition');
