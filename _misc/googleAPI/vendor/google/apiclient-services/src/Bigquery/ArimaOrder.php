<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Bigquery;

class ArimaOrder extends \Google\Model
{
  /**
   * @var string
   */
  public $d;
  /**
   * @var string
   */
  public $p;
  /**
   * @var string
   */
  public $q;

  /**
   * @param string
   */
  public function setD($d)
  {
    $this->d = $d;
  }
  /**
   * @return string
   */
  public function getD()
  {
    return $this->d;
  }
  /**
   * @param string
   */
  public function setP($p)
  {
    $this->p = $p;
  }
  /**
   * @return string
   */
  public function getP()
  {
    return $this->p;
  }
  /**
   * @param string
   */
  public function setQ($q)
  {
    $this->q = $q;
  }
  /**
   * @return string
   */
  public function getQ()
  {
    return $this->q;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ArimaOrder::class, 'Google_Service_Bigquery_ArimaOrder');
