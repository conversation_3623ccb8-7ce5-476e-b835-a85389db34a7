<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudIAP\Resource;

/**
 * The "iap_tunnel" collection of methods.
 * Typical usage is:
 *  <code>
 *   $iapService = new Google\Service\CloudIAP(...);
 *   $iap_tunnel = $iapService->projects_iap_tunnel;
 *  </code>
 */
class ProjectsIapTunnel extends \Google\Service\Resource
{
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsIapTunnel::class, 'Google_Service_CloudIAP_Resource_ProjectsIapTunnel');
