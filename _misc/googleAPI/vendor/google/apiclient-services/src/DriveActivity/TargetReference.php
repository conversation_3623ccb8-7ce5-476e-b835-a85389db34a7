<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DriveActivity;

class TargetReference extends \Google\Model
{
  protected $driveType = DriveReference::class;
  protected $driveDataType = '';
  protected $driveItemType = DriveItemReference::class;
  protected $driveItemDataType = '';
  protected $teamDriveType = TeamDriveReference::class;
  protected $teamDriveDataType = '';

  /**
   * @param DriveReference
   */
  public function setDrive(DriveReference $drive)
  {
    $this->drive = $drive;
  }
  /**
   * @return DriveReference
   */
  public function getDrive()
  {
    return $this->drive;
  }
  /**
   * @param DriveItemReference
   */
  public function setDriveItem(DriveItemReference $driveItem)
  {
    $this->driveItem = $driveItem;
  }
  /**
   * @return DriveItemReference
   */
  public function getDriveItem()
  {
    return $this->driveItem;
  }
  /**
   * @param TeamDriveReference
   */
  public function setTeamDrive(TeamDriveReference $teamDrive)
  {
    $this->teamDrive = $teamDrive;
  }
  /**
   * @return TeamDriveReference
   */
  public function getTeamDrive()
  {
    return $this->teamDrive;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(TargetReference::class, 'Google_Service_DriveActivity_TargetReference');
