<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Apigee;

class GoogleCloudApigeeV1DeploymentChangeReport extends \Google\Collection
{
  protected $collection_key = 'routingConflicts';
  protected $routingChangesType = GoogleCloudApigeeV1DeploymentChangeReportRoutingChange::class;
  protected $routingChangesDataType = 'array';
  protected $routingConflictsType = GoogleCloudApigeeV1DeploymentChangeReportRoutingConflict::class;
  protected $routingConflictsDataType = 'array';
  protected $validationErrorsType = GoogleRpcPreconditionFailure::class;
  protected $validationErrorsDataType = '';

  /**
   * @param GoogleCloudApigeeV1DeploymentChangeReportRoutingChange[]
   */
  public function setRoutingChanges($routingChanges)
  {
    $this->routingChanges = $routingChanges;
  }
  /**
   * @return GoogleCloudApigeeV1DeploymentChangeReportRoutingChange[]
   */
  public function getRoutingChanges()
  {
    return $this->routingChanges;
  }
  /**
   * @param GoogleCloudApigeeV1DeploymentChangeReportRoutingConflict[]
   */
  public function setRoutingConflicts($routingConflicts)
  {
    $this->routingConflicts = $routingConflicts;
  }
  /**
   * @return GoogleCloudApigeeV1DeploymentChangeReportRoutingConflict[]
   */
  public function getRoutingConflicts()
  {
    return $this->routingConflicts;
  }
  /**
   * @param GoogleRpcPreconditionFailure
   */
  public function setValidationErrors(GoogleRpcPreconditionFailure $validationErrors)
  {
    $this->validationErrors = $validationErrors;
  }
  /**
   * @return GoogleRpcPreconditionFailure
   */
  public function getValidationErrors()
  {
    return $this->validationErrors;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudApigeeV1DeploymentChangeReport::class, 'Google_Service_Apigee_GoogleCloudApigeeV1DeploymentChangeReport');
