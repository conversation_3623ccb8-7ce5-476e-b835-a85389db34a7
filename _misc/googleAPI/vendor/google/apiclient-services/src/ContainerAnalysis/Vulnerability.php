<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\ContainerAnalysis;

class Vulnerability extends \Google\Collection
{
  protected $collection_key = 'windowsDetails';
  public $cvssScore;
  protected $cvssV3Type = CVSSv3::class;
  protected $cvssV3DataType = '';
  protected $detailsType = Detail::class;
  protected $detailsDataType = 'array';
  public $severity;
  public $sourceUpdateTime;
  protected $windowsDetailsType = WindowsDetail::class;
  protected $windowsDetailsDataType = 'array';

  public function setCvssScore($cvssScore)
  {
    $this->cvssScore = $cvssScore;
  }
  public function getCvssScore()
  {
    return $this->cvssScore;
  }
  /**
   * @param CVSSv3
   */
  public function setCvssV3(CVSSv3 $cvssV3)
  {
    $this->cvssV3 = $cvssV3;
  }
  /**
   * @return CVSSv3
   */
  public function getCvssV3()
  {
    return $this->cvssV3;
  }
  /**
   * @param Detail[]
   */
  public function setDetails($details)
  {
    $this->details = $details;
  }
  /**
   * @return Detail[]
   */
  public function getDetails()
  {
    return $this->details;
  }
  public function setSeverity($severity)
  {
    $this->severity = $severity;
  }
  public function getSeverity()
  {
    return $this->severity;
  }
  public function setSourceUpdateTime($sourceUpdateTime)
  {
    $this->sourceUpdateTime = $sourceUpdateTime;
  }
  public function getSourceUpdateTime()
  {
    return $this->sourceUpdateTime;
  }
  /**
   * @param WindowsDetail[]
   */
  public function setWindowsDetails($windowsDetails)
  {
    $this->windowsDetails = $windowsDetails;
  }
  /**
   * @return WindowsDetail[]
   */
  public function getWindowsDetails()
  {
    return $this->windowsDetails;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Vulnerability::class, 'Google_Service_ContainerAnalysis_Vulnerability');
