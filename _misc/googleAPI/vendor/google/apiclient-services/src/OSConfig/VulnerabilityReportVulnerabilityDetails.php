<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\OSConfig;

class VulnerabilityReportVulnerabilityDetails extends \Google\Collection
{
  protected $collection_key = 'references';
  /**
   * @var string
   */
  public $cve;
  /**
   * @var float
   */
  public $cvssV2Score;
  protected $cvssV3Type = CVSSv3::class;
  protected $cvssV3DataType = '';
  /**
   * @var string
   */
  public $description;
  protected $referencesType = VulnerabilityReportVulnerabilityDetailsReference::class;
  protected $referencesDataType = 'array';
  /**
   * @var string
   */
  public $severity;

  /**
   * @param string
   */
  public function setCve($cve)
  {
    $this->cve = $cve;
  }
  /**
   * @return string
   */
  public function getCve()
  {
    return $this->cve;
  }
  /**
   * @param float
   */
  public function setCvssV2Score($cvssV2Score)
  {
    $this->cvssV2Score = $cvssV2Score;
  }
  /**
   * @return float
   */
  public function getCvssV2Score()
  {
    return $this->cvssV2Score;
  }
  /**
   * @param CVSSv3
   */
  public function setCvssV3(CVSSv3 $cvssV3)
  {
    $this->cvssV3 = $cvssV3;
  }
  /**
   * @return CVSSv3
   */
  public function getCvssV3()
  {
    return $this->cvssV3;
  }
  /**
   * @param string
   */
  public function setDescription($description)
  {
    $this->description = $description;
  }
  /**
   * @return string
   */
  public function getDescription()
  {
    return $this->description;
  }
  /**
   * @param VulnerabilityReportVulnerabilityDetailsReference[]
   */
  public function setReferences($references)
  {
    $this->references = $references;
  }
  /**
   * @return VulnerabilityReportVulnerabilityDetailsReference[]
   */
  public function getReferences()
  {
    return $this->references;
  }
  /**
   * @param string
   */
  public function setSeverity($severity)
  {
    $this->severity = $severity;
  }
  /**
   * @return string
   */
  public function getSeverity()
  {
    return $this->severity;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(VulnerabilityReportVulnerabilityDetails::class, 'Google_Service_OSConfig_VulnerabilityReportVulnerabilityDetails');
