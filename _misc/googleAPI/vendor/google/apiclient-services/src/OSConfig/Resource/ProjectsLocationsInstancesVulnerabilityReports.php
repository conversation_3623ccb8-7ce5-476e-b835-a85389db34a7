<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\OSConfig\Resource;

use Google\Service\OSConfig\ListVulnerabilityReportsResponse;
use Google\Service\OSConfig\VulnerabilityReport;

/**
 * The "vulnerabilityReports" collection of methods.
 * Typical usage is:
 *  <code>
 *   $osconfigService = new Google\Service\OSConfig(...);
 *   $vulnerabilityReports = $osconfigService->projects_locations_instances_vulnerabilityReports;
 *  </code>
 */
class ProjectsLocationsInstancesVulnerabilityReports extends \Google\Service\Resource
{
  /**
   * Gets the vulnerability report for the specified VM instance. Only VMs with
   * inventory data have vulnerability reports associated with them.
   * (vulnerabilityReports.get)
   *
   * @param string $name Required. API resource name for vulnerability resource.
   * Format: `projects/{project}/locations/{location}/instances/{instance}/vulnera
   * bilityReport` For `{project}`, either `project-number` or `project-id` can be
   * provided. For `{instance}`, either Compute Engine `instance-id` or `instance-
   * name` can be provided.
   * @param array $optParams Optional parameters.
   * @return VulnerabilityReport
   * @throws \Google\Service\Exception
   */
  public function get($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], VulnerabilityReport::class);
  }
  /**
   * List vulnerability reports for all VM instances in the specified zone.
   * (vulnerabilityReports.listProjectsLocationsInstancesVulnerabilityReports)
   *
   * @param string $parent Required. The parent resource name. Format:
   * `projects/{project}/locations/{location}/instances/-` For `{project}`, either
   * `project-number` or `project-id` can be provided.
   * @param array $optParams Optional parameters.
   *
   * @opt_param string filter This field supports filtering by the severity level
   * for the vulnerability. For a list of severity levels, see [Severity levels
   * for vulnerabilities](https://cloud.google.com/container-
   * analysis/docs/container-scanning-
   * overview#severity_levels_for_vulnerabilities). The filter field follows the
   * rules described in the [AIP-160](https://google.aip.dev/160) guidelines as
   * follows: + **Filter for a specific severity type**: you can list reports that
   * contain vulnerabilities that are classified as medium by specifying
   * `vulnerabilities.details.severity:MEDIUM`. + **Filter for a range of
   * severities** : you can list reports that have vulnerabilities that are
   * classified as critical or high by specifying
   * `vulnerabilities.details.severity:HIGH OR
   * vulnerabilities.details.severity:CRITICAL`
   * @opt_param int pageSize The maximum number of results to return.
   * @opt_param string pageToken A pagination token returned from a previous call
   * to `ListVulnerabilityReports` that indicates where this listing should
   * continue from.
   * @return ListVulnerabilityReportsResponse
   * @throws \Google\Service\Exception
   */
  public function listProjectsLocationsInstancesVulnerabilityReports($parent, $optParams = [])
  {
    $params = ['parent' => $parent];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], ListVulnerabilityReportsResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsLocationsInstancesVulnerabilityReports::class, 'Google_Service_OSConfig_Resource_ProjectsLocationsInstancesVulnerabilityReports');
