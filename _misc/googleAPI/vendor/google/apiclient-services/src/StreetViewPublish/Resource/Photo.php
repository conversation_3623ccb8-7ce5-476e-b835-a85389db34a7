<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\StreetViewPublish\Resource;

use Google\Service\StreetViewPublish\Photo as PhotoModel;
use Google\Service\StreetViewPublish\StreetviewpublishEmpty;
use Google\Service\StreetViewPublish\UploadRef;

/**
 * The "photo" collection of methods.
 * Typical usage is:
 *  <code>
 *   $streetviewpublishService = new Google\Service\StreetViewPublish(...);
 *   $photo = $streetviewpublishService->photo;
 *  </code>
 */
class Photo extends \Google\Service\Resource
{
  /**
   * After the client finishes uploading the photo with the returned UploadRef,
   * CreatePhoto publishes the uploaded Photo to Street View on Google Maps.
   * Currently, the only way to set heading, pitch, and roll in CreatePhoto is
   * through the [Photo Sphere XMP
   * metadata](https://developers.google.com/streetview/spherical-metadata) in the
   * photo bytes. CreatePhoto ignores the `pose.heading`, `pose.pitch`,
   * `pose.roll`, `pose.altitude`, and `pose.level` fields in Pose. This method
   * returns the following error codes: * google.rpc.Code.INVALID_ARGUMENT if the
   * request is malformed or if the uploaded photo is not a 360 photo. *
   * google.rpc.Code.NOT_FOUND if the upload reference does not exist. *
   * google.rpc.Code.RESOURCE_EXHAUSTED if the account has reached the storage
   * limit. (photo.create)
   *
   * @param PhotoModel $postBody
   * @param array $optParams Optional parameters.
   * @return PhotoModel
   * @throws \Google\Service\Exception
   */
  public function create(PhotoModel $postBody, $optParams = [])
  {
    $params = ['postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('create', [$params], PhotoModel::class);
  }
  /**
   * Deletes a Photo and its metadata. This method returns the following error
   * codes: * google.rpc.Code.PERMISSION_DENIED if the requesting user did not
   * create the requested photo. * google.rpc.Code.NOT_FOUND if the photo ID does
   * not exist. (photo.delete)
   *
   * @param string $photoId Required. ID of the Photo.
   * @param array $optParams Optional parameters.
   * @return StreetviewpublishEmpty
   * @throws \Google\Service\Exception
   */
  public function delete($photoId, $optParams = [])
  {
    $params = ['photoId' => $photoId];
    $params = array_merge($params, $optParams);
    return $this->call('delete', [$params], StreetviewpublishEmpty::class);
  }
  /**
   * Gets the metadata of the specified Photo. This method returns the following
   * error codes: * google.rpc.Code.PERMISSION_DENIED if the requesting user did
   * not create the requested Photo. * google.rpc.Code.NOT_FOUND if the requested
   * Photo does not exist. * google.rpc.Code.UNAVAILABLE if the requested Photo is
   * still being indexed. (photo.get)
   *
   * @param string $photoId Required. ID of the Photo.
   * @param array $optParams Optional parameters.
   *
   * @opt_param string languageCode The BCP-47 language code, such as "en-US" or
   * "sr-Latn". For more information, see
   * http://www.unicode.org/reports/tr35/#Unicode_locale_identifier. If
   * language_code is unspecified, the user's language preference for Google
   * services is used.
   * @opt_param string view Required. Specifies if a download URL for the photo
   * bytes should be returned in the Photo response.
   * @return PhotoModel
   * @throws \Google\Service\Exception
   */
  public function get($photoId, $optParams = [])
  {
    $params = ['photoId' => $photoId];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], PhotoModel::class);
  }
  /**
   * Creates an upload session to start uploading photo bytes. The method uses the
   * upload URL of the returned UploadRef to upload the bytes for the Photo. In
   * addition to the photo requirements shown in
   * https://support.google.com/maps/answer/7012050?ref_topic=6275604, the photo
   * must meet the following requirements: * Photo Sphere XMP metadata must be
   * included in the photo metadata. See
   * https://developers.google.com/streetview/spherical-metadata for the required
   * fields. * The pixel size of the photo must meet the size requirements listed
   * in https://support.google.com/maps/answer/7012050?ref_topic=6275604, and the
   * photo must be a full 360 horizontally. After the upload completes, the method
   * uses UploadRef with CreatePhoto to create the Photo object entry.
   * (photo.startUpload)
   *
   * @param StreetviewpublishEmpty $postBody
   * @param array $optParams Optional parameters.
   * @return UploadRef
   * @throws \Google\Service\Exception
   */
  public function startUpload(StreetviewpublishEmpty $postBody, $optParams = [])
  {
    $params = ['postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('startUpload', [$params], UploadRef::class);
  }
  /**
   * Updates the metadata of a Photo, such as pose, place association,
   * connections, etc. Changing the pixels of a photo is not supported. Only the
   * fields specified in the updateMask field are used. If `updateMask` is not
   * present, the update applies to all fields. This method returns the following
   * error codes: * google.rpc.Code.PERMISSION_DENIED if the requesting user did
   * not create the requested photo. * google.rpc.Code.INVALID_ARGUMENT if the
   * request is malformed. * google.rpc.Code.NOT_FOUND if the requested photo does
   * not exist. * google.rpc.Code.UNAVAILABLE if the requested Photo is still
   * being indexed. (photo.update)
   *
   * @param string $id A unique identifier for a photo.
   * @param PhotoModel $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param string updateMask Required. Mask that identifies fields on the
   * photo metadata to update. If not present, the old Photo metadata is entirely
   * replaced with the new Photo metadata in this request. The update fails if
   * invalid fields are specified. Multiple fields can be specified in a comma-
   * delimited list. The following fields are valid: * `pose.heading` *
   * `pose.lat_lng_pair` * `pose.pitch` * `pose.roll` * `pose.level` *
   * `pose.altitude` * `connections` * `places` > Note: When updateMask contains
   * repeated fields, the entire set of repeated values get replaced with the new
   * contents. For example, if updateMask contains `connections` and
   * `UpdatePhotoRequest.photo.connections` is empty, all connections are removed.
   * @return PhotoModel
   * @throws \Google\Service\Exception
   */
  public function update($id, PhotoModel $postBody, $optParams = [])
  {
    $params = ['id' => $id, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('update', [$params], PhotoModel::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Photo::class, 'Google_Service_StreetViewPublish_Resource_Photo');
