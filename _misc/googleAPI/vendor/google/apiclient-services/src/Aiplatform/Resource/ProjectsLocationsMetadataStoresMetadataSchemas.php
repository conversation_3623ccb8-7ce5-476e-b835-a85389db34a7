<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform\Resource;

use Google\Service\Aiplatform\GoogleCloudAiplatformV1ListMetadataSchemasResponse;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1MetadataSchema;

/**
 * The "metadataSchemas" collection of methods.
 * Typical usage is:
 *  <code>
 *   $aiplatformService = new Google\Service\Aiplatform(...);
 *   $metadataSchemas = $aiplatformService->projects_locations_metadataStores_metadataSchemas;
 *  </code>
 */
class ProjectsLocationsMetadataStoresMetadataSchemas extends \Google\Service\Resource
{
  /**
   * Creates a MetadataSchema. (metadataSchemas.create)
   *
   * @param string $parent Required. The resource name of the MetadataStore where
   * the MetadataSchema should be created. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param GoogleCloudAiplatformV1MetadataSchema $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param string metadataSchemaId The {metadata_schema} portion of the
   * resource name with the format: `projects/{project}/locations/{location}/metad
   * ataStores/{metadatastore}/metadataSchemas/{metadataschema}` If not provided,
   * the MetadataStore's ID will be a UUID generated by the service. Must be 4-128
   * characters in length. Valid characters are `/a-z-/`. Must be unique across
   * all MetadataSchemas in the parent Location. (Otherwise the request will fail
   * with ALREADY_EXISTS, or PERMISSION_DENIED if the caller can't view the
   * preexisting MetadataSchema.)
   * @return GoogleCloudAiplatformV1MetadataSchema
   * @throws \Google\Service\Exception
   */
  public function create($parent, GoogleCloudAiplatformV1MetadataSchema $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('create', [$params], GoogleCloudAiplatformV1MetadataSchema::class);
  }
  /**
   * Retrieves a specific MetadataSchema. (metadataSchemas.get)
   *
   * @param string $name Required. The resource name of the MetadataSchema to
   * retrieve. Format: `projects/{project}/locations/{location}/metadataStores/{me
   * tadatastore}/metadataSchemas/{metadataschema}`
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1MetadataSchema
   * @throws \Google\Service\Exception
   */
  public function get($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], GoogleCloudAiplatformV1MetadataSchema::class);
  }
  /**
   * Lists MetadataSchemas.
   * (metadataSchemas.listProjectsLocationsMetadataStoresMetadataSchemas)
   *
   * @param string $parent Required. The MetadataStore whose MetadataSchemas
   * should be listed. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param string filter A query to filter available MetadataSchemas for
   * matching results.
   * @opt_param int pageSize The maximum number of MetadataSchemas to return. The
   * service may return fewer. Must be in range 1-1000, inclusive. Defaults to
   * 100.
   * @opt_param string pageToken A page token, received from a previous
   * MetadataService.ListMetadataSchemas call. Provide this to retrieve the next
   * page. When paginating, all other provided parameters must match the call that
   * provided the page token. (Otherwise the request will fail with
   * INVALID_ARGUMENT error.)
   * @return GoogleCloudAiplatformV1ListMetadataSchemasResponse
   * @throws \Google\Service\Exception
   */
  public function listProjectsLocationsMetadataStoresMetadataSchemas($parent, $optParams = [])
  {
    $params = ['parent' => $parent];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], GoogleCloudAiplatformV1ListMetadataSchemasResponse::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsLocationsMetadataStoresMetadataSchemas::class, 'Google_Service_Aiplatform_Resource_ProjectsLocationsMetadataStoresMetadataSchemas');
