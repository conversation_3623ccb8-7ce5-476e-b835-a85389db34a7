<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Aiplatform\Resource;

use Google\Service\Aiplatform\GoogleCloudAiplatformV1AddExecutionEventsRequest;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1AddExecutionEventsResponse;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1Execution;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1LineageSubgraph;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1ListExecutionsResponse;
use Google\Service\Aiplatform\GoogleCloudAiplatformV1PurgeExecutionsRequest;
use Google\Service\Aiplatform\GoogleLongrunningOperation;

/**
 * The "executions" collection of methods.
 * Typical usage is:
 *  <code>
 *   $aiplatformService = new Google\Service\Aiplatform(...);
 *   $executions = $aiplatformService->projects_locations_metadataStores_executions;
 *  </code>
 */
class ProjectsLocationsMetadataStoresExecutions extends \Google\Service\Resource
{
  /**
   * Adds Events to the specified Execution. An Event indicates whether an
   * Artifact was used as an input or output for an Execution. If an Event already
   * exists between the Execution and the Artifact, the Event is skipped.
   * (executions.addExecutionEvents)
   *
   * @param string $execution Required. The resource name of the Execution that
   * the Events connect Artifacts with. Format: `projects/{project}/locations/{loc
   * ation}/metadataStores/{metadatastore}/executions/{execution}`
   * @param GoogleCloudAiplatformV1AddExecutionEventsRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1AddExecutionEventsResponse
   * @throws \Google\Service\Exception
   */
  public function addExecutionEvents($execution, GoogleCloudAiplatformV1AddExecutionEventsRequest $postBody, $optParams = [])
  {
    $params = ['execution' => $execution, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('addExecutionEvents', [$params], GoogleCloudAiplatformV1AddExecutionEventsResponse::class);
  }
  /**
   * Creates an Execution associated with a MetadataStore. (executions.create)
   *
   * @param string $parent Required. The resource name of the MetadataStore where
   * the Execution should be created. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param GoogleCloudAiplatformV1Execution $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param string executionId The {execution} portion of the resource name
   * with the format: `projects/{project}/locations/{location}/metadataStores/{met
   * adatastore}/executions/{execution}` If not provided, the Execution's ID will
   * be a UUID generated by the service. Must be 4-128 characters in length. Valid
   * characters are `/a-z-/`. Must be unique across all Executions in the parent
   * MetadataStore. (Otherwise the request will fail with ALREADY_EXISTS, or
   * PERMISSION_DENIED if the caller can't view the preexisting Execution.)
   * @return GoogleCloudAiplatformV1Execution
   * @throws \Google\Service\Exception
   */
  public function create($parent, GoogleCloudAiplatformV1Execution $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('create', [$params], GoogleCloudAiplatformV1Execution::class);
  }
  /**
   * Deletes an Execution. (executions.delete)
   *
   * @param string $name Required. The resource name of the Execution to delete.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/executions/{execution}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param string etag Optional. The etag of the Execution to delete. If this
   * is provided, it must match the server's etag. Otherwise, the request will
   * fail with a FAILED_PRECONDITION.
   * @return GoogleLongrunningOperation
   * @throws \Google\Service\Exception
   */
  public function delete($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('delete', [$params], GoogleLongrunningOperation::class);
  }
  /**
   * Retrieves a specific Execution. (executions.get)
   *
   * @param string $name Required. The resource name of the Execution to retrieve.
   * Format: `projects/{project}/locations/{location}/metadataStores/{metadatastor
   * e}/executions/{execution}`
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1Execution
   * @throws \Google\Service\Exception
   */
  public function get($name, $optParams = [])
  {
    $params = ['name' => $name];
    $params = array_merge($params, $optParams);
    return $this->call('get', [$params], GoogleCloudAiplatformV1Execution::class);
  }
  /**
   * Lists Executions in the MetadataStore.
   * (executions.listProjectsLocationsMetadataStoresExecutions)
   *
   * @param string $parent Required. The MetadataStore whose Executions should be
   * listed. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param array $optParams Optional parameters.
   *
   * @opt_param string filter Filter specifying the boolean condition for the
   * Executions to satisfy in order to be part of the result set. The syntax to
   * define filter query is based on https://google.aip.dev/160. Following are the
   * supported set of filters: * **Attribute filtering**: For example:
   * `display_name = "test"`. Supported fields include: `name`, `display_name`,
   * `state`, `schema_title`, `create_time`, and `update_time`. Time fields, such
   * as `create_time` and `update_time`, require values specified in RFC-3339
   * format. For example: `create_time = "2020-11-19T11:30:00-04:00"`. *
   * **Metadata field**: To filter on metadata fields use traversal operation as
   * follows: `metadata..` For example: `metadata.field_1.number_value = 10.0` In
   * case the field name contains special characters (such as colon), one can
   * embed it inside double quote. For example: `metadata."field:1".number_value =
   * 10.0` * **Context based filtering**: To filter Executions based on the
   * contexts to which they belong use the function operator with the full
   * resource name: `in_context()`. For example:
   * `in_context("projects//locations//metadataStores//contexts/")` Each of the
   * above supported filters can be combined together using logical operators
   * (`AND` & `OR`). Maximum nested expression depth allowed is 5. For example:
   * `display_name = "test" AND metadata.field1.bool_value = true`.
   * @opt_param string orderBy How the list of messages is ordered. Specify the
   * values to order by and an ordering operation. The default sorting order is
   * ascending. To specify descending order for a field, users append a " desc"
   * suffix; for example: "foo desc, bar". Subfields are specified with a `.`
   * character, such as foo.bar. see https://google.aip.dev/132#ordering for more
   * details.
   * @opt_param int pageSize The maximum number of Executions to return. The
   * service may return fewer. Must be in range 1-1000, inclusive. Defaults to
   * 100.
   * @opt_param string pageToken A page token, received from a previous
   * MetadataService.ListExecutions call. Provide this to retrieve the subsequent
   * page. When paginating, all other provided parameters must match the call that
   * provided the page token. (Otherwise the request will fail with an
   * INVALID_ARGUMENT error.)
   * @return GoogleCloudAiplatformV1ListExecutionsResponse
   * @throws \Google\Service\Exception
   */
  public function listProjectsLocationsMetadataStoresExecutions($parent, $optParams = [])
  {
    $params = ['parent' => $parent];
    $params = array_merge($params, $optParams);
    return $this->call('list', [$params], GoogleCloudAiplatformV1ListExecutionsResponse::class);
  }
  /**
   * Updates a stored Execution. (executions.patch)
   *
   * @param string $name Output only. The resource name of the Execution.
   * @param GoogleCloudAiplatformV1Execution $postBody
   * @param array $optParams Optional parameters.
   *
   * @opt_param bool allowMissing If set to true, and the Execution is not found,
   * a new Execution is created.
   * @opt_param string updateMask Optional. A FieldMask indicating which fields
   * should be updated.
   * @return GoogleCloudAiplatformV1Execution
   * @throws \Google\Service\Exception
   */
  public function patch($name, GoogleCloudAiplatformV1Execution $postBody, $optParams = [])
  {
    $params = ['name' => $name, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('patch', [$params], GoogleCloudAiplatformV1Execution::class);
  }
  /**
   * Purges Executions. (executions.purge)
   *
   * @param string $parent Required. The metadata store to purge Executions from.
   * Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}`
   * @param GoogleCloudAiplatformV1PurgeExecutionsRequest $postBody
   * @param array $optParams Optional parameters.
   * @return GoogleLongrunningOperation
   * @throws \Google\Service\Exception
   */
  public function purge($parent, GoogleCloudAiplatformV1PurgeExecutionsRequest $postBody, $optParams = [])
  {
    $params = ['parent' => $parent, 'postBody' => $postBody];
    $params = array_merge($params, $optParams);
    return $this->call('purge', [$params], GoogleLongrunningOperation::class);
  }
  /**
   * Obtains the set of input and output Artifacts for this Execution, in the form
   * of LineageSubgraph that also contains the Execution and connecting Events.
   * (executions.queryExecutionInputsAndOutputs)
   *
   * @param string $execution Required. The resource name of the Execution whose
   * input and output Artifacts should be retrieved as a LineageSubgraph. Format:
   * `projects/{project}/locations/{location}/metadataStores/{metadatastore}/execu
   * tions/{execution}`
   * @param array $optParams Optional parameters.
   * @return GoogleCloudAiplatformV1LineageSubgraph
   * @throws \Google\Service\Exception
   */
  public function queryExecutionInputsAndOutputs($execution, $optParams = [])
  {
    $params = ['execution' => $execution];
    $params = array_merge($params, $optParams);
    return $this->call('queryExecutionInputsAndOutputs', [$params], GoogleCloudAiplatformV1LineageSubgraph::class);
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ProjectsLocationsMetadataStoresExecutions::class, 'Google_Service_Aiplatform_Resource_ProjectsLocationsMetadataStoresExecutions');
