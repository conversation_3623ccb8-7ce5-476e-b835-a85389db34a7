<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\CloudSearch;

class TimestampOperatorOptions extends \Google\Model
{
  /**
   * @var string
   */
  public $greaterThanOperatorName;
  /**
   * @var string
   */
  public $lessThanOperatorName;
  /**
   * @var string
   */
  public $operatorName;

  /**
   * @param string
   */
  public function setGreaterThanOperatorName($greaterThanOperatorName)
  {
    $this->greaterThanOperatorName = $greaterThanOperatorName;
  }
  /**
   * @return string
   */
  public function getGreaterThanOperatorName()
  {
    return $this->greaterThanOperatorName;
  }
  /**
   * @param string
   */
  public function setLessThanOperatorName($lessThanOperatorName)
  {
    $this->lessThanOperatorName = $lessThanOperatorName;
  }
  /**
   * @return string
   */
  public function getLessThanOperatorName()
  {
    return $this->lessThanOperatorName;
  }
  /**
   * @param string
   */
  public function setOperatorName($operatorName)
  {
    $this->operatorName = $operatorName;
  }
  /**
   * @return string
   */
  public function getOperatorName()
  {
    return $this->operatorName;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(TimestampOperatorOptions::class, 'Google_Service_CloudSearch_TimestampOperatorOptions');
