.redux-main {
    input{
        &.redux-color {
            float: left;
            width: 70px;
            margin-left: 5px;
        }

        &.color-transparency {
            margin-left: 10px;
            margin-right: 3px;
        }

        &.wp-color-picker {
            width: 80px !important;
        }
    }

    .section-color {
        .controls {
            width: 345px;
        }

        .explain {
            width: 225px;
        }
    }

    .iris-picker {
        .iris-strip .ui-slider-handle {
            position: absolute;
            background: none !important;
            right: -3px;
            left: -3px;
            border: 4px solid #aaa !important;
            border-width: 4px 3px;
            width: auto;
            border-radius: 4px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, .2);
            opacity: .9;
            z-index: 5;
            cursor: ns-resize;
        }

        .iris-slider-offset {
            position: absolute;
            top: 2px;
            left: 0px;
            right: 0;
            bottom: 4px;
            width: 28px; //17
            background: none !important;
            border: 0 !important;
            height: auto;
        }
    }

    .wp-picker-container {
        display: inline-block;
        outline: 0;

        input {
            margin-bottom: inherit;
            margin-top: inherit;
            padding: 3px 5px;
        }

        .wp-color-result {
            outline: 0;
            margin: 0;
            height: 24px!important;
            margin: 0 6px 6px 0!important;
        }

        .wp-picker-default {
            padding: 0 10px 1px;
        }

        .wp-color-result-text {
            line-height: 22px;
        }
    }

    .redux-color-gradient {
        line-height: 24px;
    }

    .color-transparency-check {
        line-height: 1;
        margin: 0!important;
        padding-top: 10px;
    }

    .wp-picker-clear {
        margin-top: 0 !important;
    }
}

.wp-customizer {
    .redux-main input.wp-picker-default, .redux-main .redux-typography-container input.wp-picker-default, .redux-main .redux-typography-container .redux-typography-color {
        padding: 0px 4px !important;
    }
    .redux-main input.wp-color-picker {
        width: 65px !important;
        margin-left: 5px !important;
    }
}