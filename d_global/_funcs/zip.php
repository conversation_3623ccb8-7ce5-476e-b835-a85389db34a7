<?php

function createAndServeZip($files = 'array of file paths', $filename = 'somename.zip', $path = false, $deleteOld = true){
	$zip_path = createZip($files, $filename, $path, $deleteOld);//	d($zip); ... just some object...
	serveZip($zip_path, $filename);
}

/**
 * Creates a zip file from an array of file paths.
 * @param   array  $files  An array of file paths to be zipped.
 * @param   string  $filename  The name of the zip file.
 * @param   string|bool  $path  The path where the zip file should be created. If false, the tmp is used.
 * @param   bool  $deleteOld  Whether to delete an old zip file if it exists. just as a cleanup.
 * @return string The filepath to the created zip file.
 */

function createZip($files = 'array of file paths', $filename = 'somename.zip', $path = false, $deleteOld = true){
	$zip = new ZipArchive();

	if( !$path){
		$path = pathtotmp.'createZip_temp_deleteme/';
	}//best to keep them in a folder so that if your temp dir freaks out, you'll know its this func, or not this func
	mkdirbetter($path);

	$zip_path = slashbetweennotend($path, $filename);

	if($deleteOld){
		@unlink($zip_path);
	}//delete teh old, make a new... idk about this, need to test.

	if($zip->open($zip_path, ZIPARCHIVE::CREATE) !== true){
		$error .= "* Sorry ZIP creation failed at this time";
		die($error);
	}

	foreach($files as $f){
		$zip->addFile($f);                 // Adding files into zip
		$zip->renameName($f, basename($f));// or else the full path is recreated in  the zip
	}

	$zip->close();

	return $zip_path;
}

// $zippath is the whole path, plus the filename.  You can send in a different filename if you want.
function serveZip($zip_path, $filename = false){
	$filename = ($filename) ? : basename($zip_path);
	ob_end_clean();
	if(headers_sent()){
		echo 'HTTP header already sent';
		return false;
	}
	if( !is_file($zip_path)){
		header($_SERVER['SERVER_PROTOCOL'].' 404 Not Found');
		echo 'File not found';
	}elseif( !is_readable($zip_path)){
		header($_SERVER['SERVER_PROTOCOL'].' 403 Forbidden');
		echo 'File not readable';
	}else{
		header("Pragma: public");
		header("Expires: 0");
		header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
		header("Cache-Control: public");
		header("Content-Description: File Transfer");
		header("Content-type: application/octet-stream");
		header("Content-Disposition: attachment; filename=\"".$filename."\"");
		header("Content-Transfer-Encoding: binary");
		header("Content-Length: ".filesize($zip_path));
		readfile($zip_path);
		exit;
	}
}

// When $extractFolder is true, the contents are first extracted to a temporary directory.
//			The structure is inspected to determine if it contains exactly one folder.
//    		If one folder exists, its contents are moved to $targetDir.
// Preserve Original Behavior:
//    		If $extractFolder is false, the entire structure is extracted directly into $targetDir.
// mostly works, unused. function extractZipToFolder()
function extractZipToFolder($zipFile, $targetDir, $extractFolder = true){
	if( !file_exists($zipFile)){
		throw new Exception("Zip file does not exist: $zipFile");
	}

	if( !is_dir($targetDir) && !mkdir($targetDir, 0777, true)){
		throw new Exception("Failed to create target directory: $targetDir");
	}

	$zip = new ZipArchive();
	if($zip->open($zipFile) !== true){
		throw new Exception("Failed to open zip file: $zipFile");
	}

	// if there is just the one dir, extract the files from that dir into the target dir
	// If not a single folder, extract entire structure to the target directory
	if($extractFolder){
		// Extract to a temporary directory for inspection
		$tempDir = sys_get_temp_dir().'/zip_extract_'.uniqid();
		if( !mkdir($tempDir, 0777, true)){
			$zip->close();
			throw new Exception("Failed to create temporary directory: $tempDir");
		}
		$zip->extractTo($tempDir);

		// Check root contents of the temporary directory
		$rootContents = scandir($tempDir);
		$rootContents = array_diff($rootContents, ['.', '..']); // Remove "." and ".."

		if(count($rootContents) === 1 && is_dir($tempDir.'/'.reset($rootContents))){
			$singleFolder = $tempDir.'/'.reset($rootContents);

			// Extract contents of the single folder to the target directory
			$files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($singleFolder, RecursiveDirectoryIterator::SKIP_DOTS));
			foreach($files as $file){
				$destPath = $targetDir.'/'.$files->getSubPathName();
				if( !is_dir(dirname($destPath))){
					mkdir(dirname($destPath), 0755, true);
				}
				copy($file, $destPath);
			}
		}else{
			// If not a single folder, extract entire structure to the target directory
			$zip->extractTo($targetDir);
		}

		rmdir_recursive($tempDir);// Clean up the temporary directory

	}else{
		// Extract the entire zip structure to the target directory
		$res = $zip->extractTo($targetDir);
		if($res !== true){
			throw new Exception("Failed to extract zip file: $zipFile to $targetDir");
		}
	}
	$zip->close();
	return true;
}

