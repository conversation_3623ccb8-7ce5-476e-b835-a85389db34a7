<?php

function resizeImageToWidth($imgPath, $targetWidth, $args = []){
	extract(wp_parse_args($args, ['outputPath'      => null,
								  'outputFilename'  => null,
								  'height'          => null,
								  'jpgQuality'      => 80,
								  'pngCompression'  => 9,
								  'outputFormat'    => null, // Determines the format of the resized image
								  'filenamePrepend' => '']));

	if( !file_exists($imgPath)){
		throw new Exception("Image file does not exist: $imgPath");
	}

	// Get image dimensions and type
	[$originalWidth, $originalHeight, $imageType] = getimagesize($imgPath);

	// Calculate new height if $height is not specified
	$newHeight = $height ?? intval(($originalHeight / $originalWidth) * $targetWidth);

	// Create a new empty image with the target size
	$resizedImage = imagecreatetruecolor($targetWidth, $newHeight);

	// Load the original image based on its type
	switch($imageType){
		case IMAGETYPE_JPEG:
			$originalImage = imagecreatefromjpeg($imgPath);
			break;
		case IMAGETYPE_PNG:
			$originalImage = imagecreatefrompng($imgPath);
			imagealphablending($resizedImage, false);
			imagesavealpha($resizedImage, true); // Preserve transparency for PNG
			break;
		case IMAGETYPE_GIF:
			$originalImage = imagecreatefromgif($imgPath);
			break;
		default:
			throw new Exception("Unsupported image type: $imgPath");
	}

	// Validate that the original image was successfully loaded
	if( !$originalImage){
		throw new Exception("Failed to create image resource from file: $imgPath");
	}

	// Resize the original image into the new canvas
	imagecopyresampled($resizedImage, $originalImage, 0, 0, 0, 0, $targetWidth, $newHeight, $originalWidth, $originalHeight);

	$outputFilename = $outputFilename ?? basename($imgPath);
	$outputFilename = $filenamePrepend.$outputFilename;
	$ext            = getFileExtensionCaseInsensitive($outputFilename);
	$outputExt      = $outputFormat = $outputFormat ?? $ext;// if no output format is specified, use the extension of the original file
	$outputFilename = replaceOrAddExtension($outputFilename, $outputExt);
	$outputPath     = $outputPath ?? d_getPath($imgPath);
	$outputFilePath = slashend($outputPath).$outputFilename;


	// Convert and save the resized image based on the desired output format
	// Ensure transparency for target formats
	if($outputFormat === 'png' || $outputFormat === 'gif'){
		imagealphablending($resizedImage, false);
		imagesavealpha($resizedImage, true);
	}

	switch(strtolower($outputFormat)){
		case 'jpg':
		case 'jpeg': // Support both 'jpg' and 'jpeg'
			imagejpeg($resizedImage, $outputFilePath, $jpgQuality); // Adjust quality as needed
			break;
		case 'png':
			imagepng($resizedImage, $outputFilePath, $pngCompression); // Adjust compression level as needed
			break;
		case 'gif':
			imagegif($resizedImage, $outputFilePath);
			break;
		default:
			throw new Exception("Unsupported output format during saving: $outputFormat");
	}

	// Free up memory
	imagedestroy($originalImage);
	imagedestroy($resizedImage);

	return $outputPath;
}

function is_image_wp($filePath){
	$fileType = wp_check_filetype($filePath);
	// Check if the 'type' returned is an image MIME type
	return ihaystackcontainsneedle($fileType['type'], 'image');
}

