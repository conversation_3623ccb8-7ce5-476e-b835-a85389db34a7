//parent then ours  this names all the colors and suchlike
//@import "../../d_theme_3/css/partials/_.000-vars-values.scss";
//@import 'partials/_000-localvars-values.scss';

//parent then ours  this loads things up with color (etc) values
//@import "../../d_theme_3/css/partials/_.000-vars.scss";
//@import 'partials/_000-localvars.scss';

i have a parent/child theme.
scss for the child, I first import the parent's variables that have actual values like
$verydarkgreengrey: #273C3A;
$teal: #187177;
$red: #f2321d;

then i import my variable values, like
$verydarkgreengrey: #334455;
$teal: #995511;
$red: #112244;

then i import the parent variables that use the values like:
$fontcolor: $verydarkgreengrey;
$linkcolor: $teal;
$linkover: $red;

then i import my variables, like:
$fontcolor: $someothervar;
$linkcolor: $adifferentcolor;
$linkover: $lighterred;

then i import all the parent's styles, along with some mixins, like
.myclass{
	color: $fontcolor;
	}

then i import my styles which override/add to the parent:
.myclass{
	display: flex;
	}
or
.myclass{
	color: #ccc;
	}




