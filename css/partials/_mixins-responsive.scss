@mixin responsive-padding-01-02 {
	padding: 0 1vw;

	@include up_to_ipad {
		padding: 0 2vw;
	}
}
@mixin responsive-padding-01_5-02 {
	padding: 0 1.5vw;

	@include up_to_ipad {
		padding: 0 2vw;
	}
}
@mixin responsive-padding-01_5-02_foursides {
	padding: 1.5vw;

	@include up_to_ipad {
		padding: 2vw;
	}
}


// the parentheses () in a mixin definition are only required if the mixin takes arguments.
// if it has () can you still call it without, if you dont need to send args?  No — if the mixin is defined with parentheses, you must include the parentheses when calling it, even if you're not passing any arguments.
// &{ } refers to the parent selector, but @content already respects nesting, so wrapping it in & {} just nests it redundantly.
//In fact, leaving the & block can sometimes cause unexpected selector nesting or extra specificity.
//
//$atMedia_ipad_max-width: 768px;
//$atMedia_ipad_min-width: 769px;
//$small_max-width: 550px; //ok if you do this (not in quotes) , you can add +5 to it. orwhatever
//$small_min-width: 551px;
//$wrapper_max-width: ($wrapperpx + $wrapperpxBuffer);
//$wrapper_min-width: ($wrapperpx + $wrapperpxBuffer +1);
@mixin up_to_ipad {
	@media screen and (max-width: $atMedia_ipad_max-width) {
		@content;
	}
}
@mixin more_than_ipad {
	@media screen and (min-width: $atMedia_ipad_min-width) {
		@content;
	}
}
@mixin up_to_wrapper {
	@media screen and (max-width: ($wrapperpx + $wrapperpxBuffer)) {
		@content;
	}
}
@mixin more_than_wrapper {
	@media screen and (min-width: ($wrapperpx + $wrapperpxBuffer) +1) {
		@content;
	}
}
@mixin up_to_small {
	@media only screen and (max-width: $small_max-width) {
		@content;
	}
}
@mixin more_than_small {
	@media screen and (min-width: $wrapper_min-width) {
		@content;
	}
}

@mixin up_to_max($max-width) {
	@media screen and (max-width: $max-width) {
		@content;
	}
}

@mixin more_than_min($min-width) {
	@media screen and (min-width: $min-width) {
		@content;
	}
}

// hiders and showers
//- hide elements on everything "iPad size" and smaller).
.hide768, .hideIpad {
	@include up_to_ipad {
		display: none;
	}
}
// - hide elements on everything larger than "iPad size").
.show768, .hideLarge {
	@include more_than_ipad {
		display: none;
	}
}
// hide elements on everything larger than 1000px
.hide-above-wrapper {
	@include more_than_wrapper {
		display: none;
	}
}

.show-above-wrapper {
	@include up_to_wrapper {
		display: none;
	}
}

