//
//
// this one is more modern.  just use all px
@mixin fluid-type-clamp($min-size, $max-size, $min-vw: $small_min-width, $max-vw: $wrapperpx) {
	@if unit($min-size) != px or unit($max-size) != px {
		@error "Font sizes must be in px.";
	}
	@if unit($min-vw) != px or unit($max-vw) != px {
		@error "Viewport widths must be in px.";
	}

	$slope: (strip-unit($max-size - $min-size) / strip-unit($max-vw - $min-vw)) * 100;
	$intercept: strip-unit($min-size) - ($slope * strip-unit($min-vw) / 100);

	font-size: clamp(
		#{$min-size},
		calc(#{$intercept}px + #{$slope}vw),
		#{$max-size}
	);
}

// old https://css-tricks.com/snippets/css/fluid-typography/
@mixin fluid-type($max-font-size, $min-font-size:12px, $min-vw:550px, $max-vw:1100px) {
	@include fluid-type-clamp($min-font-size, $max-font-size, $min-vw, $max-vw);
	//$u1: unit($min-vw);
	//$u2: unit($max-vw);
	//$u3: unit($min-font-size);
	//$u4: unit($max-font-size);
	//
	//@if $u1 == $u2 and $u1 == $u3 and $u1 == $u4 {
	//	& {
	//		font-size: $min-font-size;
	//		@media screen and (min-width: $min-vw) {
	//			font-size: calc(#{$min-font-size} + #{strip-unit($max-font-size - $min-font-size)} * ((100vw - #{$min-vw}) / #{strip-unit($max-vw - $min-vw)}));
	//			--devnote: "minvw";
	//		}
	//		@media screen and (min-width: $max-vw) {
	//			font-size: $max-font-size;
	//			--devnote: "maxvw";
	//		}
	//	}
	//}
}


// .cards{
//		container-type: inline-size
//		// optional container-name: card
//  	h2 {
//			@include fluid-container-type(18px, 26px, 225px, 350px); // Min size 18px, max 26px
// 		.subtitle {
//			@include fluid-container-type(14px, 18px, 225px, 350px); // Min size 14px, max 18px
@mixin fluid-container-type(
	$min-size,
	$max-size,
	$min-breakpoint: $small_min-width,
	$max-breakpoint: $wrapper_max-width,
	$container-name: null//optional. if you send it in, it will use that name for the container query. otherwise, it will use the
	// default container query which is the nearest parent with a container-type: inline-size (or other.)
) {
	// Base size (fallback)
	font-size: $min-size;

	// Container query implementation with discrete steps
	@supports (container-type: inline-size) {
		$breakpoint-range: strip-unit($max-breakpoint) - strip-unit($min-breakpoint);
		$size-range: strip-unit($max-size) - strip-unit($min-size);
		$steps: (0.2, 0.4, 0.6, 0.8, 1);

		@each $step in $steps {
			$bp: strip-unit($min-breakpoint) + ($breakpoint-range * $step);
			$size: strip-unit($min-size) + ($size-range * $step);

			// Use max values for the final step, calculated values for others
			$final-bp: if($step == 1, strip-unit($max-breakpoint), $bp);
			$final-size: if($step == 1, $max-size, #{$size}px);

			// Generate container query with or without name
			$container-prefix: if($container-name, "#{$container-name} ", ""); //spaces are important.
			@container #{$container-prefix}(min-width: #{$final-bp}px) {
				font-size: $final-size;
			}
		}
	}
}

//You provide an array of padding values (as many as you want)
//The mixin calculates evenly spaced breakpoints between your min and max container widths
//Each padding value is applied at its corresponding breakpoint
//
//For example, if you provide:
//		5 padding values
//		Min container width of 250px
// 		Max container width of 400px
// The mixin will create container queries at:
//		250px (first padding value)
//		287.5px (second padding value)
//		325px (third padding value)
//		362.5px (fourth padding value)
//		400px (fifth padding value)
//
//You can use it with any number of padding values: The more values you provide, the more
//granular control you have over how the padding changes as the container width increases.
// Fluid padding based on container width using predefined steps
// Define your padding values for each step
// $padding-steps: (2vw 0, 3vw 0, 4vw 6px, 4vw, 6vw);
// @include fluid-container-padding($padding-steps, 250px, 400px);
@mixin fluid-container-padding(
	$padding-values,
	$min-breakpoint: $small_min-width,
	$max-breakpoint: $wrapper_max-width,
	$container-name: null//optional. if you send it in, it will use that name for the container query. otherwise, it will use the
	// default container query which is the nearest parent with a container-type: inline-size (or other.)
) {
	// Base padding (fallback) - use the last value in the array
	padding: nth($padding-values, length($padding-values));

	// Container query implementation with discrete steps
	@supports (container-type: inline-size) {
		$breakpoint-range: strip-unit($max-breakpoint) - strip-unit($min-breakpoint);
		$steps-count: length($padding-values);

		@for $i from 1 through $steps-count {
			$step-percentage: ($i - 1) / ($steps-count - 1); // 								// Calculate the step percentage (0 to 1)
			$bp: strip-unit($min-breakpoint) + ($breakpoint-range * $step-percentage); // // Calculate the breakpoint for this step
			$final-bp: if($i == $steps-count, strip-unit($max-breakpoint), $bp); // 		// Use max breakpoint for the final step
			$padding-value: nth($padding-values, $i); // 								    // Get the padding value for this step

			// Generate container query with or without name
			$container-prefix: if($container-name, "#{$container-name} ", "");
			@container #{$container-prefix}(min-width: #{$final-bp}px) {
				padding: $padding-value;
			}
		}
	}
}
.asdf {display: none;}

