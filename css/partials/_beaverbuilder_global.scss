//<editor-fold desc="-- zero out margins and padding -- ">
#body_wrapper {
	.fl-builder-module-template {
		padding: 0; // where a module is imported with shortcode and that's all there is.
	}

	.fl-builder-content-primary { // maybe any .fl-builder-content  but prolly just the primary
		& > .fl-row { //, & > .fl-row-content
			margin-bottom: $wrapper_padding;

			// ech,dont do this. &:last-child { margin-bottom: 0; }

			.fl-row-content-wrap { // this is crap i think: .fl-row-layout-full-fixed .fl-row-fixed-width > .fl-col-group {
				margin-left: 0 !important;
				margin-right: 0 !important;
				padding: 0 !important;

				// remove padding and margin all to Zero
				.fl-col {
					padding: 0 ($wrapper_padding / 2);

					// yes, this makes cols work, when there are multiple
					// but also, on only one, both paddings are 0 (cuz its first and last)
					&:first-child {
						padding-left: 0;
					}

					&:last-child {
						padding-right: 0;
					}

					p:last-child {
						margin-bottom: 0;
					}

					.fl-post-grid {
						margin-left: 0;
						margin-right: 0;
					}
				}

				// ok, this is for when columns stack.  They stack at the 'small' breakpoint.
				@media (max-width: $small_max-width) {
					& .fl-col-small:not(.fl-col-small-full-width) {
						max-width: 100%;
						visibility: visible;
						padding: 0;

						margin: ($wrapper_padding * 4) 0;

						&:first-child {
							margin: 0 0 ($wrapper_padding * 4) 0;
						}
					}
				}
			}
		}
	}

	// wrapper stuff.  yes, i think this is good.  fl-row-full-width will be fullwidth on its own
	.fl-row.fl-row-fixed-width {
		padding: 0;

		// leave this
		.fl-row-fixed-width { // this is for inner rows, like  .fl-row-fixed-width .fl-row-fixed-width {}
			padding: 0;
		}
	}
}
//</editor-fold> -- zero out margins and padding --


// if you have a row on a fullwidth page, but you need the content to be padded, then use fullbutpadded on the row.
//<editor-fold desc="-- fullbutpadded -- ">
body.fullwidth div.fl-row.fl-row-fixed-width,
body.fullwidth #body_container #body_wrapper div.fl-row.fl-row-full-width.fullbutpadded div.fl-row-content-wrap {
	@include up_to_wrapper {
		padding: 0 $wrapper_padding !important;
	}
}
//</editor-fold> -- fullbutpadded --

//<editor-fold desc="-- yes, we need this margin zero -- ">

.fl-builder-content,
.fl-builder-template,
.fl-builder-row-template {
	.fl-module-content {
		margin: 0 !important;
	}
}
//</editor-fold> -- yes, we need this margin zero --


//<editor-fold desc="-- line heights, font sizes etc -- ">
div.fl-module-post-grid {
	div.fl-post-column {
		.fl-post-grid-text {
			padding: 1vw !important;

			.fl-post-grid-title {
				@include fluid-type(22px, 18px);
				line-height: 1.2 !important;

				&, a {
					text-decoration: none !important;
				}
			}

			.fl-post-grid-meta,
			.fl-post-grid-content {
				&, p, span {
					@include fluid-type(17px, 15px);
					line-height: 1.4 !important;
				}
			}
		}
	}
}
//</editor-fold> -- line heights, font sizes etc --


//better spacing for post grids
//<editor-fold desc="-- mixins -- ">

//$beaverbuilder_LargeMaxWidth: 1200px; //up_to_large {@media screen and (max-width: $beaverbuilder_LargeMaxWidth)
//$beaverbuilder_LargeMinWidth: ($beaverbuilder_LargeMaxWidth + 1); //more_than_large {@media screen and (min-width: $beaverbuilder_LargeMinWidth)
//
//$beaverbuilder_MediumMaxWidth: 992px;
//$beaverbuilder_MediumMinWidth: ($beaverbuilder_MediumMaxWidth + 1);
//
//$beaverbuilder_SmallMaxWidth: 768px;
//$beaverbuilder_SmallMinWidth: ($beaverbuilder_SmallMaxWidth + 1);
@mixin bb_up_to_large {
	@media screen and (max-width: $beaverbuilder_LargeMaxWidth) {
		@content;
	}
}
@mixin bb_more_than_large {
	@media screen and (min-width: $beaverbuilder_LargeMinWidth) {
		@content;
	}
}
@mixin bb_up_to_medium {
	@media screen and (max-width: $beaverbuilder_MediumMaxWidth) {
		@content;
	}
}
@mixin bb_more_than_medium {
	@media screen and (min-width: $beaverbuilder_MediumMinWidth) {
		@content;
	}
}
@mixin bb_up_to_small {
	@media screen and (max-width: $beaverbuilder_SmallMaxWidth) {
		@content;
	}
}
@mixin bb_more_than_small {
	@media screen and (min-width: $beaverbuilder_SmallMinWidth) {
		@content;
	}
}
//</editor-fold> -- mixins --

//<editor-fold desc="-- padding for cols disabled. try grid -- ">
div.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX { //, div.fl-post-grid.masonry
	div.fl-post-column { //, div.fl-post-grid-post
		//with 2% margin,
		//5 col	18.39
		//4 col	22.99
		//3 col	31.32
		//2 col	47.95

		padding: 0 !important;
		width: 18.39% !important;
		margin-bottom: 2%;
		margin-right: 2%;

		&:nth-child(5n) {
			margin-right: 0 !important;
		}


		@include bb_up_to_large { // 4 cols
			width: 22.99% !important;
			&:nth-child(4n) {
				margin-right: 0 !important;
			}
		}
		@include bb_up_to_medium { // 3 cols
			width: 31.32% !important;
			&:nth-child(3n) {
				margin-right: 0 !important;
			}
		}
		@include bb_up_to_small { // 2 cols
			width: 47.95% !important;
			&:nth-child(2n) {
				margin-right: 0 !important;
			}
		}
	}
}
//</editor-fold> -- padding for cols --

div.fl-post-grid { //, div.fl-post-grid.masonry
	@include gridColumns5432;
	gap: 1.5vw;
	padding: 0 !important;
	margin: 0 auto !important;


	div.fl-post-column {
		width: auto;
		min-width: 0;
		padding: 0 !important;
		margin: 0 !important;
	}

	&:before,
	&:after,
	&:not([data-accepts]):before,
	&:not([data-accepts]):after {
		// i think this was just for clearfix.  but floats are for chumps.
		display: none !important;
	}

}


.fl-module-heading .fl-heading {
	margin-bottom: ($wrapper_padding / 2) !important;
}
