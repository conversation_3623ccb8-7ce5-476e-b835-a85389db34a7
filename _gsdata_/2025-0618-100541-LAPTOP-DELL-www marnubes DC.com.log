2025-06-18 10:31:21: Server KEXes: curve25519-sha256,<EMAIL>,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,diffie-hellman-group-exchange-sha256,diffie-hellman-group14-sha256,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group-exchange-sha1,diffie-hellman-group14-sha1,<EMAIL>
2025-06-18 10:31:21: Chosen KEX: <EMAIL>
2025-06-18 10:31:21: Server Hostkey Algorithms: rsa-sha2-512,rsa-sha2-256,ssh-rsa,ecdsa-sha2-nistp256,ssh-ed25519
2025-06-18 10:31:21: Chosen Hostkey Algorithm: ecdsa-sha2-nistp256
2025-06-18 10:31:21: Server Client->Server Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:21: Chosen Client->Server Cipher: aes256-ctr
2025-06-18 10:31:21: Server Server->Client Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:21: Chosen Server->Client Cipher: aes256-ctr
2025-06-18 10:31:21: Server Client->Server MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:21: Chosen Client->Server MAC: hmac-sha2-256
2025-06-18 10:31:21: Server Server->Client MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:21: Chosen Server->Client MAC: hmac-sha2-256
2025-06-18 10:31:21: SSH_MSG_EXT_INFO: server-sig-algs: ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521
2025-06-18 10:31:22: == Sync Started by User Command
2025-06-18 10:31:22: ConnectCloned connected to file:///C:/_d/websites/dougcassidy.com/www
2025-06-18 10:31:22: ConnectCloned connected to file:///C:/_d/websites/dougcassidy.com/www
2025-06-18 10:31:22: ConnectCloned connected to file:///C:/_d/websites/dougcassidy.com/www
2025-06-18 10:31:22: Server KEXes: curve25519-sha256,<EMAIL>,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,diffie-hellman-group-exchange-sha256,diffie-hellman-group14-sha256,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group-exchange-sha1,diffie-hellman-group14-sha1,<EMAIL>
2025-06-18 10:31:22: Chosen KEX: <EMAIL>
2025-06-18 10:31:22: Server Hostkey Algorithms: rsa-sha2-512,rsa-sha2-256,ssh-rsa,ecdsa-sha2-nistp256,ssh-ed25519
2025-06-18 10:31:22: Chosen Hostkey Algorithm: ecdsa-sha2-nistp256
2025-06-18 10:31:22: Server Client->Server Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:22: Chosen Client->Server Cipher: aes256-ctr
2025-06-18 10:31:22: Server Server->Client Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:22: Chosen Server->Client Cipher: aes256-ctr
2025-06-18 10:31:22: Server Client->Server MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:22: Chosen Client->Server MAC: hmac-sha2-256
2025-06-18 10:31:22: Server Server->Client MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:22: Chosen Server->Client MAC: hmac-sha2-256
2025-06-18 10:31:22: Server KEXes: curve25519-sha256,<EMAIL>,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,diffie-hellman-group-exchange-sha256,diffie-hellman-group14-sha256,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group-exchange-sha1,diffie-hellman-group14-sha1,<EMAIL>
2025-06-18 10:31:22: Chosen KEX: <EMAIL>
2025-06-18 10:31:22: Server Hostkey Algorithms: rsa-sha2-512,rsa-sha2-256,ssh-rsa,ecdsa-sha2-nistp256,ssh-ed25519
2025-06-18 10:31:22: Chosen Hostkey Algorithm: ecdsa-sha2-nistp256
2025-06-18 10:31:22: Server Client->Server Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:22: Chosen Client->Server Cipher: aes256-ctr
2025-06-18 10:31:22: Server Server->Client Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:22: Chosen Server->Client Cipher: aes256-ctr
2025-06-18 10:31:22: Server Client->Server MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:22: Chosen Client->Server MAC: hmac-sha2-256
2025-06-18 10:31:22: Server Server->Client MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:22: Chosen Server->Client MAC: hmac-sha2-256
2025-06-18 10:31:22: Server KEXes: curve25519-sha256,<EMAIL>,ecdh-sha2-nistp256,ecdh-sha2-nistp384,ecdh-sha2-nistp521,diffie-hellman-group-exchange-sha256,diffie-hellman-group14-sha256,diffie-hellman-group16-sha512,diffie-hellman-group18-sha512,diffie-hellman-group-exchange-sha1,diffie-hellman-group14-sha1,<EMAIL>
2025-06-18 10:31:22: Chosen KEX: <EMAIL>
2025-06-18 10:31:22: Server Hostkey Algorithms: rsa-sha2-512,rsa-sha2-256,ssh-rsa,ecdsa-sha2-nistp256,ssh-ed25519
2025-06-18 10:31:22: Chosen Hostkey Algorithm: ecdsa-sha2-nistp256
2025-06-18 10:31:22: Server Client->Server Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:22: Chosen Client->Server Cipher: aes256-ctr
2025-06-18 10:31:22: Server Server->Client Ciphers: <EMAIL>,<EMAIL>,aes256-ctr,aes256-cbc,<EMAIL>,aes128-ctr,aes128-cbc
2025-06-18 10:31:22: Chosen Server->Client Cipher: aes256-ctr
2025-06-18 10:31:22: Server Client->Server MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:22: Chosen Client->Server MAC: hmac-sha2-256
2025-06-18 10:31:22: Server Server->Client MACs: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,hmac-sha2-256,hmac-sha1,<EMAIL>,hmac-sha2-512
2025-06-18 10:31:22: Chosen Server->Client MAC: hmac-sha2-256
2025-06-18 10:31:22: SSH_MSG_EXT_INFO: server-sig-algs: ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521
2025-06-18 10:31:22: SSH_MSG_EXT_INFO: server-sig-algs: ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521
2025-06-18 10:31:22: SSH_MSG_EXT_INFO: server-sig-algs: ssh-ed25519,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521
2025-06-18 10:31:22: GsConnectAndSetRoot1 connected to sftp://ftp.marnubes.net/home/<USER>/public_html
2025-06-18 10:31:22: GsConnectAndSetRoot1 connected to sftp://ftp.marnubes.net/home/<USER>/public_html
2025-06-18 10:31:22: GsConnectAndSetRoot1 connected to sftp://ftp.marnubes.net/home/<USER>/public_html
2025-06-18 10:31:23: Copy Over 'C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/style.css' -> 'sftp://ftp.marnubes.net/home/<USER>/public_html/wp-content/themes/d_theme_3_child/style.css' (83,029)
2025-06-18 10:31:23: Copy Over 'C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/style.debug.css' -> 'sftp://ftp.marnubes.net/home/<USER>/public_html/wp-content/themes/d_theme_3_child/style.debug.css' (100,500)
2025-06-18 10:31:23: Copy Over 'C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/style.debug.css.map' -> 'sftp://ftp.marnubes.net/home/<USER>/public_html/wp-content/themes/d_theme_3_child/style.debug.css.map' (164,832)
2025-06-18 10:31:23: Copy Over 'C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_card.scss' -> 'sftp://ftp.marnubes.net/home/<USER>/public_html/wp-content/themes/d_theme_3/css/partials/_card.scss' (3,706)
2025-06-18 10:31:23: Copy Over 'C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_000-mixins.scss' -> 'sftp://ftp.marnubes.net/home/<USER>/public_html/wp-content/themes/d_theme_3/css/partials/_000-mixins.scss' (4,576)
2025-06-18 10:31:23: Copy Over 'C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_mixins-type.scss' -> 'sftp://ftp.marnubes.net/home/<USER>/public_html/wp-content/themes/d_theme_3/css/partials/_mixins-type.scss' (5,486)
2025-06-18 10:31:23: Save State File C:/_d/websites/dougcassidy.com/www/_gsdata_/_file_state_v4._gs
2025-06-18 10:31:24: Save State File sftp://ftp.marnubes.net/home/<USER>/public_html/_gsdata_/_file_state_v4._gs
2025-06-18 10:31:24: == Sync Complete. Time: 00:00:02, Speed: 0.00/s, OK: 6, Errors: 0
