<?php
$config['plugin_screen'] = 'settings_page_ucp';
$config['icon_border'] = '2px solid #ff7900';
$config['icon_right'] = '40px';
$config['icon_bottom'] = '30px';
$config['icon_image'] = 'ucp.png';
$config['icon_padding'] = '6px';
$config['icon_size'] = '55px';
$config['menu_accent_color'] = '#ff7920';
$config['custom_css'] = '#wf-flyout .wpfssl-icon .wff-icon img { max-width: 66%; } #wf-flyout .wpfssl-icon .wff-icon { line-height: 57px; }';

$config['menu_items'] = array(
  array('href' => 'https://underconstructionpage.com/?ref=wff-ucp&coupon=welcome', 'target' => '_blank', 'label' => 'Get UnderConstrucionPage PRO with 20% OFF', 'icon' => 'dashicons-star-filled', 'class' => 'accent'),
  array('href' => 'https://getwpcaptcha.com/?ref=wff-ucp', 'label' => 'Protect your site from spam &amp; brute-force attacks', 'icon' => 'wp-captcha.png', 'class' => 'captcha-icon'),
  array('href' => 'https://wpforcessl.com/?ref=wff-ucp', 'label' => 'Fix all SSL problems &amp; monitor site in real-time', 'icon' => 'wp-ssl.png', 'class' => 'wpfssl-icon'),
  array('href' => 'https://wp301redirects.com/?ref=wff-ucp&coupon=50off', 'label' => 'Fix 2 most common SEO issues on WordPress', 'icon' => '301-logo.png', 'class' => 'wp301-icon'),
  array('href' => 'https://wpsticky.com/?ref=wff-ucp', 'target' => '_blank', 'label' => 'Make a menu sticky with WP Sticky', 'icon' => 'dashicons-admin-post'),
  array('href' => 'https://wpreset.com/?ref=wff-ucp', 'target' => '_blank', 'label' => 'Need to start over? Get WP Reset', 'icon' => 'wp-reset.png'),
  array('href' => 'https://wordpress.org/support/plugin/under-construction-page/reviews/?filter=5#new-post', 'target' => '_blank', 'label' => 'Rate the Plugin', 'icon' => 'dashicons-thumbs-up'),
  array('href' => 'https://wordpress.org/support/plugin/under-construction-page/', 'target' => '_blank', 'label' => 'Get Support', 'icon' => 'dashicons-sos'),
);
