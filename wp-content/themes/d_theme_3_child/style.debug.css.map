{"version": 3, "sources": ["data:;charset=utf-8,/*!%20%20This%20(!)%20needed%20to%20keep%20the%20comment%20in%20there,%20for%20WP%0D%0A%09Template:%20%20d_theme_3%0D%0A%09Theme%20Name:%20%20%20d_theme_3_child%0D%0A*/%0D%0A/%20%20must%20be%20forward%20slashes%0D%0A%0D%0A/%3Ceditor-fold%20desc=%22--%20vars%20and%20importers%20--%20%22%3E%0D%0A%0D%0A/d_theme_3/css/partials/_.000-vars-values.scss%22;%0D%0A@import%20'partials/_000-localvars-values.scss';%0D%0A%0D%0A/d_theme_3/css/partials/_.000-vars.scss%22;%0D%0A@import%20'partials/_000-localvars.scss';%0D%0A/%0D%0A/d_theme_3/css/partials/_.000-importer.scss';%0D%0A%0D%0A/%3C/editor-fold%3E%20--%20vars%20and%20importers%20--%0D%0A%0D%0A/mu-plugins/d_inc/d_gallery/css/mu-plugins/d_inc/d_gallery/css/mu-plugins/d_inc/_misc/CalendarListView/_calendarListView.scss';%0D%0A/%3C/editor-fold%3E%20--%20plugin%20stuff%20first,%20so%20can%20be%20overrided%20--%0D%0A%0D%0A/%20override%20whatever%0D%0A@import%20'partials/_header.scss';%0D%0A%0D%0A@import%20'partials/_theme.scss';%0D%0A@import%20'partials/_headers.scss';%0D%0A@import%20'partials/_home.scss';%0D%0A@import%20'partials/_pages.scss';%0D%0A@import%20'partials/_nav_wprmenu.scss';%0D%0A@import%20'partials/_products_portfolio.scss';%0D%0A%0D%0A%0D%0A%0D%0A%0D%0A", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_mixins-responsive.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_mixins-type.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_000-mixins.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_010_reset.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_015_normalize.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_000-localvars-values.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_.000-vars-values.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_d_flex_grid.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_base.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_mixins-links.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_base_comment.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_forms.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_debug.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_tables.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_tables_zebra.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_000-localvars.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_card.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_.000-vars.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_mixins-grid.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_theme.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_nav_wprmenu.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_nav.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_nav_main_menu_flex.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_headers.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_buttons.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_footer.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_header.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_container-wrapper.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3/css/partials/_beaverbuilder_global.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/mu-plugins/d_inc/d_gallery/css/partials/_d_gallery.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/mu-plugins/d_inc/d_gallery/css/partials/_glightbox.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/mu-plugins/d_inc/_misc/CalendarListView/_calendarListView.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_header.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_theme.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_headers.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_home.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_nav_wprmenu.scss", "file:/C:/_d/websites/dougcassidy.com/www/wp-content/themes/d_theme_3_child/css/partials/_products_portfolio.scss"], "names": [], "mappings": ";AAAA;AAAA;AAAA;AAAA;ACmCC;EA4CD;IAEE;;;;AAzCD;EA6CD;IAEE;;;;AArCD;EAyCD;IAEE;;;;AAhDD;EAoDD;IAEE;;;;ACqCF;EAAO;;;ACqDP;EAAO;;;AC3LP;EACC;EACA;EACA;EACA;EACA;;;AAID;EAEC;EACA;EACA;;;AAWD;EACC;;;AAGD;EACC;;;AAID;EACC;;;AAID;EDsFC;;AALA;EACC;EACA;EACA;;;AC/EF;AAAA;EDqEC,iBARmB;EASnB,oBATmB;EAUnB,YAVmB;;;ACxDpB;EACC;EACA;;;AAID;EACC;EACA;EACA;EACA;;;AAID;EACC;EACA;;AAEA;EACC;EACA;EACA;;;AAMF;EACC;EACA;EACA;;AAEA;EACC;EACA;EACA;;;AAKF;EACC;;AAEA;EACC;EACA;EACA;;AC9FF;EACC,aCCM;EDAN;EACA;EACA;;;AAID;EACC;EACA;EACA;EAEA,kBEUO;EFTP;EACA;EACA;EAEA,aChBM;EDiBN,aAZgB;EAahB,OCRO;EDSP;EAEA;EACA;;;AAID;EACC;;;AAGD;EACC;;;AAGD;EACC;EACA;;;AAID;EACC;;;AAID;EACC;;;AAGD;EACC;EACA;;;AAID;EACC;;;AAKD;EACC;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;EACA;;;AAID;EACC;EACA;EACA;;;AA0BD;EACC;EACA;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;EACA;;;AAGD;EACC;;;AAGD;EACC;EACA;EACA;EACA;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;EACA;;AAEA;EACC;EACA;;;AAIF;AAAA;AAAA;EAGC;EACA;EACA;EACA;;;AAID;EACC;;;AAOD;EACC;EACA;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;EACA;;;AAGD;EACC;;;AAID;EACC;;;AAID;AAAA;AAAA;AAAA;EAIC;EACA;EASA;;AANA;AAAA;AAAA;AAAA;EACC;EACA;;AAQA;AAAA;AAAA;AAAA;EACC;EACA;EACA;;AAID;AAAA;AAAA;AAAA;EACC;;AAKF;AAAA;AAAA;AAAA;EACC;EACA;;;AAMF;EACC;;;AAID;EACC;;;AAID;EACC;;;AAMD;AAAA;EAEC;EACA;;;AAGD;EACC;EACA;;;AAID;EACC;EACA;;;AGrQD;AAzBQ;EACP;EACA;EACA;EACA;EACA;EAiBC;;;AAOF;EACC;EACA;EACA;EACA;EACA;EAEG;EACA;EACA;;;AAIJ;EACC;EACA;;;AAED;EAGC;EACA;EACA;EACA;;;AAQD;EACC;EACA;EAEA;EACA;EACA;EACA;EACA;;;AAGA;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;;;AAED;EACC;;;AAKD;EACC;EACA;;AACA;EACC;EACA;;AAEA;EACA;;;AAGF;EAEE;IACC;IACA;IACA;IACA;;;AAKH;EACC;EACA;;AACA;EACC;EACA;EACA;;AAEA;EACA;;;AAGF;EAEE;IACC;IACA;IACA;;EAED;IACC;;EAED;IACC;;;AAIH;EAEE;IACC;IACA;IACA;;EAED;IACC;;EAED;IACC;;EAED;IACC;;EAED;IACC;;;AASH;EACC;EACA;EACA;EACA;;;AAED;EACC;IACC;IACA;IACA;;EAED;AAAA;IAEC;;;AAGD;EACC;IACC;;EAED;IACC;IACA;;;AAGH;AC/LA;EC+GC,OJlGO;EImGP;;AAEA;EAEC,OJvGM;EIwGN;;AAED;EACC,OJ7GS;EI8GT;;AAGD;EAEC,OJjHM;;;AGVR;EACC;;;AAIA;EC2BA;;ADvBA;EC2BA;;;ADtBA;ECkBA;;ADdA;ECcA;;;ADTD;AAGA;EACC;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;;;AAKD;EACC;EACA,OFzBO;;AE2BP;EC4DA,OD3DgB;EC4DhB;;AAEA;EAEC,ODhEe;ECiEf;;AAED;EACC,ODpE0C;ECqE1C;;AAGD;EAEC,OD1Ee;;;AAKhB;EACC;;AAEA;EACC;;AAEA;EACC;;AAGD;EACC;;AAKH;EACC;EACA;EACA;;AAID;EACC;EACA;EACA;;AAKD;EACC;EACA;EACA;EACA;;AAOA;EACC;AAAA;AAAA;AAAA;AAAA;IACC;;;AAGF;EACC;AAAA;AAAA;AAAA;AAAA;IACC;;;AAGF;EACC;AAAA;AAAA;AAAA;AAAA;IACC;IACA;IACA;IACA;IACA;;;AAMH;EACC;EACA;EACA;EACA;EACA,OF/FS;;AEqGV;EACC;EACA;EACA;EACA;;;AAKF;EAEC;EACA,uBFxHO;EGuFP,OJlGO;EImGP;;ADkCA;ECnCA,OJlGO;EImGP;;AAEA;EAEC,OJvGM;EIwGN;;AAED;EACC,OD2B2B;EC1B3B;;AAGD;EAEC,OJjHM;;AIqGP;EAEC,OJvGM;EIwGN;;AAED;EACC,OJ7GS;EI8GT;;AAGD;EAEC,OJjHM;;;AG4IR;EACC,OF9HQ;EE+HR;EACA;;;AAGD;EACC;EACA;EACA;;AAEA;EACC;EACA;EACA;;AAGD;EACC;EACA;EACA;;;AE7KF;EACC;EACA;EACA;;;AAID;EACC;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;;AAEA;EACC;EACA;;AAGD;EACC;;AAEA;EACC;EACA;;AAGD;EACC;EACA;EACA;;AAEA;EACC,OJNK;EIOL;;AAEA;EACC;;AAOH;EACC;EACA;EACA;;AAKF;EACC;;AAGD;EACC;;AAID;EACC;;AAGD;EACC;EACA;;AAGD;EACC;EACA;EACA;;AAGD;EACC,kBJ1DM;;AI6DP;EACC,YJ1DO;;;AI+DR;EACC;EACA;EACA;;;AAIF;EACC;EACA;;AAQD;EACC;EACA;EACA;EACA;;;AAID;EACC;EACA;;AAEA;EACC;;AAGD;EACC,OJ7FO;EI8FP;EACA;;AAEA;EACC,OJlGM;;AIsGR;EACC;;AAGD;EACC;EACA,kBJhHO;EIiHP;;AAGD;EACC;EACA;;AAGD;EACC;;AAYF;EACC;;;AAGD;EACC;;;ACvKD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAmBC,OLaQ;EKZR,kBLIO;EKFP;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,aLGM;EKFN;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC;EACA;EACA;;AAID;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC;EACA,cLpBK;EKqBL;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,kBLzBI;;;AK8BP;EACC;EACA;EAEA;EACA;EAEA,cLpCS;EKqCT;EACA;EAEA,YL9CO;EK+CP;EACA;EACA;EAEA;;AAIA;EACC;;;AAMD;EACC;;;AAKF;EACC;;;AAGD;EACC;;;AAID;EACC;EACA;;AAEA;EACC;;;AAKF;EACC;;;AAGD;EACC;EACA;EACA;;;AAGD;EACC;;;AAGD;EACC;EACA;EACA,OLxGO;;;AK8GP;EACC;;AAGD;EACC;EACA,OLjGS;;AKoGV;EACC,OLrGS;EKsGT;EACA;;AAGD;EACC;EACA;;AAGD;AAAA;EAEC;EACA;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOC;EACA;EACA,OLjJM;;AKoJP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAiBC;;AAGD;EACC;EACA;;;AClMF;EACC;EACA;EACA;EACA;EH2GA,OGpGe;EHqGf;;AG1GA;EHyGA,OGxGgB;EHyGhB;EGxGC;;AH0GD;EAEC,OG7Ge;EH8Gf;;AAED;EACC,OGjHqB;EHkHrB;;AAGD;EAEC,OGvHe;;AH2GhB;EAEC,OGzGc;EH0Gd;;AAED;EACC,OG7GoB;EH8GpB;;AAGD;EAEC,OGnHc;;AAEd;EACC;EHiGF,OGhGiB;EHiGjB;EGhGE;;AHkGF;EAEC,OGrGgB;EHsGhB;;AAED;EACC,OGzGsB;EH0GtB;;AAGD;EAEC,OG/GgB;;AAGf;EH6FF,OG5FkB;EH6FlB;EG5FG;;AH8FH;EAEC,OGjGiB;EHkGjB;;AAED;EACC,OGrGuB;EHsGvB;;AAGD;EAEC,OG3GiB;;;AAOnB;EACC;;;AAGD;EACC;;;AC/BD;EACC;EACA;;AAEA;EACC;;AAEA;EACC,kBPmBU;;AOhBX;EACC;;AAKF;EACC;EACA;EACA,OPYO;EOXP;EACA;;;AAKF;AACA;EACC;EACA;EACA;;;AAGD;EACC;;;AAGD;EACC;EACA;;;ACxCD;AAAA;AAAA;EAGC;;AAEA;AAAA;AAAA;EACC,kBCiCsB;;AD9BvB;AAAA;AAAA;EACC,kBRcM;;AQXP;AAAA;AAAA;AAAA;AAAA;EAEC;EACA;;AAGD;AAAA;AAAA;EACC,kBTdO;ESeP;EACA;EACA;EACA;EACA,ORDM;;;AQkBN;AAAA;AAAA;EACC;;AAGD;AAAA;AAAA;EACC;;AAIF;AAAA;AAAA;EACC,cCT2B;;ADW3B;AAAA;AAAA;EACC;;AAIF;AAAA;AAAA;EACC;;AAEA;AAAA;AAAA;EACC;;;AAUF;AAAA;AAAA;EACC;;;AEzEF;EdOC;EACA;;AcLA;EdSA;EACA,WeqEW;EfpEX;EACA;;AF4BA;EgBxCA;IdeC;;;AcZA;EEyED;EACA;EAjFA;;AlBkCA;EgB3BC;IEJA;;;AlBmDD;EgB/CC;IEDA,UDgFgB;;;AjB1CjB;EgBrCC;IE6EA;;;AlBlDD;EgB3BC;IEgFA;;;AlBjCD;EgB/CC;IEmFA;;;;AF3CF;EdHC;EAqFA;Ec/EA;EAGA;EACA;;AAEA;EPuDA,OJlGO;EImGP;;AAEA;EAEC,OJvGM;EIwGN;;AAED;EACC,OJ3GM;EI4GN;;AAGD;EAEC,OJjHM;;AIkIP;AAAA;AAAA;AAAA;AAAA;AAAA;EAMC,OJ5IO;;AI8IP;AAAA;AAAA;AAAA;AAAA;AAAA;EAxCD,OJtGQ;EIuGR;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OJ3GO;EI4GP;;AAED;AAAA;AAAA;AAAA;AAAA;AAAA;EACC,OJ7GS;EI8GT;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OJrHO;EIwHN,uBOlEiB;;APiGnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAjDA,OJtGQ;EIuGR;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OJ3GO;EI4GP;;AAED;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EACC,OJ7GS;EI8GT;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OJrHO;EIwHN,uBOlEiB;;AAIlB;EACC;EACA;;AAEA;EACC;;AAIF;EACC;;AAEA;EACC;;AAGD;EhB5EF;;AA2BA;EgBiDE;IhBzED;;;AgB+EG;EACC;EACA;EAEA;EAGA;AACA;;AAID;EACC;EACA;EACA;EACA;EAEA;Ef/CL,WegDmC;;Af7CnC;EAeE;IeuBE;MftBD,WALY;;;EAIb;IeuBE;MftBD,WALY;;;EAIb;IeuBE;MftBD,WALY;;;EAIb;IeuBE;MftBD,WALY;;;EAIb;IeuBE;MftBD,WALY;;;;AewCZ;EACC;;AAKD;EACC;EACA;;;AAoCJ;EACC;EACA;;AAGC;EACC;;AAIF;EACC;;AAGD;EACC;EACA;;;AG9KH;EACC,OdQQ;;;AcLT;EACC;EACA;;;AAGD;EACC;EACA;EACA;;;AAGD;EACC;;;AAID;EACC;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACC;EACA;EACA;EACA;;;AAIF;EACC;EACA;;AAEA;EACC;EACA;EACA;;;ACjDF;EACC;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA,QHmEgB;EGlEhB;EACA,OfCO;EeAP;;AAEA;EXgGA,OJlGO;EImGP;;AAEA;EAEC,OJvGM;EIwGN;;AAED;EACC,OWxG+B;EXyG/B;;AAGD;EAEC,OJjHM;;AeUP;EACC;EACA;EACA;;AAEA;EACC;EACA;;AAIF;EACC;EACA;EACA;EACA;EACA,aLhCY;EKiCZ;EACA;EACA,Of7BM;;Ae8BN;EATD;IAUE;;;AAED;EAZD;IAaE;;;AAIF;EACC;EACA;EACA;;AAEA;EACC;;AAGD;EACC;EACA;EACA;EACA,OdxCK;EcyCL,aL1DW;;AK6DZ;AAAA;AAAA;EAGC,Yd/CK;EcgDL;EACA;;AAGD;EACC;;AAGD;EACC;;AAGD;EACC;;AAKF;EACC;;;AAMF;EACC;;;AAGD;EACC;EACA;EACA,YdhFO;EciFP;;AAMA;EACC,kBdxFM;EcyFN;;AAGA;EACC;;AAID;EACC,Of7GK;Ee8GL;;AAEA;EXdF,OWekB;EXdlB;EWeG;EACA,aLzHU;EK0HV;EACA;EACA;EACA;;AXlBH;EAEC,OWUiB;EXTjB;;AAED;EACC,OWM4C;EXL5C;;AAGD;EAEC;;AWSC;EACC;;AAGD;EACC,kBdnHM;;AcqHN;EACC;;;AAmBJ;EACC;IACC,aHtFa;;;;AG6Ff;EACC;IACC,aHhGc;;;;AGsGhB;EADD;IAEE;;;;AAMF;EACC;IACC;;EAED;IACC;;;AAIF;EACC;IACC;;EAED;IACC;IACA;;;AAIF;AAAA;EAEC;;;AC1LC;EACC,aNfW;EMkBX;EZsFF,OYrFiB;EZsFjB;EYrFE,gBfuBkB;EetBlB;EACA,WfoBa;EenBb;EACA;EACA;;AZkFF;EAEC,OY1FgB;EZ2FhB;;AAED;EACC,OY9FsC;EZ+FtC;;AAGD;EAEC,OYpGgB;;AAQf;EACC;EACA;;AAGD;EACC,uBfhBI;EeiBJ;;AAQF;EACC;;AACA;EACC;;AAcD;EACC;;AAGD;EACC;EACA,YhB7DO;EgB8DP;EACA;EACA;EACA;;;ACzCH;EACC;;AAGA;EACC;EACA;EACA;EACA;EACA;;AAEA;EACC;EACA;EACA;EAEA;;AAGA;EACC;;AAGD;EACC;;AAGD;EACC;;AAGD;EACC;;AAGD;EACC,aPhES;ENwGb,OHtFS;EGuFT;EavCI;EACA;EACA;EACA;EACA;;AbqCJ;EAEC,OH3FQ;EG4FR;;AAED;EACC,OHhGM;EGiGN;;AAGD;EAEC,OHrGQ;;AgBuDN;EACC;;AAIA;EACC;EACA;;AAEA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;AAIF;EACC;EACA;EACA;EACA;EACA;EACA,YjB/FK;EiBgGL;EAEA;;AAEA;EACC;EACA;EACA;;AAEA;EACC;;AAKA;EACC;EACA;EACA;EACA;EACA;;AAID;EACC;EACA;EACA;EACA;EACA;;AAGD;EACC;;AAMJ;EACC;;AAKH;EACC;EACA;EACA,OhBlIM;EgBmIN;EACA;EACA;EACA;EACA;;;AChKJ;AAAA;AAAA;AAAA;AAAA;AAAA;EAMC;EAEA,aRDa;EQEb,aRHoB;EQIpB;EACA,OlBFQ;EkBGR;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;EACC;EdgGD,OJtGQ;EIuGR;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OJ3GO;EI4GP;;AAED;AAAA;AAAA;AAAA;AAAA;AAAA;EACC,OcxGyB;EdyGzB;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OJrHO;;;AkBoBR;AAAA;AAAA;AAAA;AAAA;AAAA;EAMC,OjBXM;;AiBaN;AAAA;AAAA;AAAA;AAAA;AAAA;EACC;EdyEF,OHvFO;EGwFP;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OH5FM;EG6FN;;AAED;AAAA;AAAA;AAAA;AAAA;AAAA;EACC,OcjF8B;EdkF9B;;AAGD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAEC,OHtGM;;;AiBqBR;AAAA;AAAA;AAAA;AAAA;AAAA;EAMC;EACA;EACA,YjBGa;EiBFb,ejBGkB;EiBFlB,ajBGgB;;;AiBCjB;EACC,WNlCS;;;AMqCV;EACC,WNrCS;;;AMwCV;EACC,WNxCS;;;AM2CV;EACC,WN3CS;;;AM8CV;EACC,WN9CS;;;AMiDV;EACC,WNjDS;;;AOZV;EAhBC;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;AAAA;EAEC;;;AAeF;AAAA;AAGA;EACC;EACA;EACA;EACA,OlBVO;EkBWP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGA;EACC,OlBtBM;EkBuBN;EACA;;AAID;EACC;;;AAMF;EAEC,kBnBrDM;;AmBuDN;EAEC;;AAGD;EACC;;;AClEF;EvBKC;EACA;EuBJA,YpBMU;EoBLV,YRgFiB;EQ/EjB,OnBkBS;EmBjBT;;AAEA;EvBFA;EACA;;AuBIC;EvBAD;EACA,WeqEW;EfpEX;EACA;EuBDE;EACA;EACA;EACA;EACA;EACA;;AzBwBF;EyB/BC;IvBMA;;;AuBGC;EhByFF,OHtFS;EGuFT;;AAEA;EAEC,OH3FQ;EG4FR;;AAED;EACC,OHhGM;EGiGN;;AAGD;EAEC,OHrGQ;;AmBCP;EACC;;AAGD;EACC;;AAEA;EACC;;AAEA;EACC;;AAGD;EACC;;;AAWL;AAAA;EACC;;;AClCF;EACC;EACA,kBrBVU;;AqBYV;ExBfA;EACA;;AwBoBC;ExBhBD;EACA,WeqEW;EfpEX;EACA;;AF4BA;E0BfC;IxBVA;;;AwBeC;EACC;EACA;;AAEA;EjByEH,OHtFS;EGuFT;EiBxEI;;AjB0EJ;EAEC,OH3FQ;EG4FR;;AAED;EACC,OHhGM;EGiGN;;AAGD;EAEC,OHrGQ;;AoBmBP;EACC;EACA;;;AC5CH;EAEC;EACA;EACA;;AAEA;EzBKD;EACA,WeqEW;EfpEX;EACA;;AF4BA;E2BpCC;IzBWA;;;AyBND;EACC;;AAEA;EACC;EACA;;A3B0BF;E2B5BC;IAKE;;;A3BaH;E2BlBC;IAQE;;;AAQD;AAAA;AAAA;EACC;;;AChCH;EACC;;AAIA;EACC,eX+Ee;;AW3Ef;EACC;EACA;EACA;;AAGA;EACC;;AAIA;EACC;;AAGD;EACC;;AAGD;EACC;;AAGD;EACC;EACA;;AAKF;EACC;IACC;IACA;IACA;IAEA;;EAEA;IACC;;;AASN;EACC;;AAGA;EACC;;;A5BpBF;E4B6BD;AAAA;IAGE;;;;AAUD;AAAA;AAAA;EACC;;;AASA;EACC;;AAEA;E3BtFF;E2BwFG;;AAEA;EACC;;AAMD;AAAA;AAAA;AAAA;E3BjGH;E2BmGI;;;AAsDJ;EAOC;EACA;EACA;EACA;;AAEA;EACC;;AA9CF;EAiCA;IAkBE;;EACA;IACC;;;AA3CH;EAuBA;IAwBE;;EACA;IACC;;;AAvCH;EAaA;IA8BE;;EACA;IACC;;;;AAOJ;EVnMC;EACA;EAXA;EU+MA;EACA;EACA;;A5B/KA;E4B2KD;IV1ME;;;AlBmDD;E4BuJD;IVvME,UDgFgB;;;AjB1CjB;E4BiKD;IV/LE;;;AlBoBD;E4B2KD;IV5LE;;;AlBqCD;E4BuJD;IVzLE;;;AUgMD;EACC;EACA;EACA;EACA;;AAGD;EAKC;;;AAMF;EACC;;;ACvOD;EACC;EACA;EACA;;AAEA;EACC;EACA;EACA;EACA;EACA;;AAEA;EACC;EACA;;AAGD;EACC;EACA;;;AAMH;EACC;EACA;;AAME;EACC;EAEA;;AAEA;EACC;EACA;EACA;;AAMJ;EACC;;AAGD;EACC;;AAGD;EACC;;AAGD;EACC;;A7BND;E6B7BD;IAuCE;IACA;;;;AAUD;EACC,OZlCyB;;AYsC1B;EACC;;AAGC;EACC;;AADD;EACC;;AADD;EACC;;AADD;EACC;;AADD;EACC;;AADD;EACC;;A7B/BH;E6BoCG;IACC;;EADD;IACC;;EADD;IACC;;EADD;IACC;;;;AASJ;EAEC;;AAEA;EACC;EACA;EACA;EACA;EACA;EACA;EACA,OxBtGM;EwBuGN;EACA;EACA;;A7B5DF;E6BkDC;IAYE;;;AAIF;EACC;EACA;;AAGD;EACC;;AAOF;EACC;EACA;EACA;EACA;EACA;EAEA;E3BED,e2BDwB;EACvB;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;;A7BlGD;E6B+EA;IAqBE;;;AAIF;EACC;;;AAOD;EACC;;AAGD;EACC;EACA;EACA;;AAEA;EACC;EACA;EACA,OxBzKM;EwB0KN;EACA,axBhLK;EwBkLL;EACA;;AAGD;AAAA;AAAA;EAGC;EACA;EACA;;AAGD;EACC;;;AAyBH;EACC;EACA;EACA;EACA;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;IACC;IACA;IACA;IACA;IACA;;;AC7OA;EACC;;AAIF;EACC;EACA;EACA;EACA;;AAEA;EAEC;EACA;;AAGD;EACC;;AAEA;EACC;;AAKH;EACC;EACA;EACA;;AAEA;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAOH;EACC;;;AAMC;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;;AAGD;EACC,OzB3EK;EyB4EL;;AAGD;EACC;;AAEA;EACC,OzBnFI;EyBoFJ;;AAGD;EACC,OzBxFI;EyByFJ;;AAMH;EACC;;;AAIF;EACC;;;AAID;EAKC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAnBA;EACC;;AAoBD;EACC;EACA;EACA;EACA;EACA;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGD;EACC;EACA;EAEA;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;;AAGD;EACC;EACA;EACA;;AAGD;EACC;EACA;;AAGD;EACC;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGD;EACC;;AAGD;AAAA;EAEC;EACA;EACA;EACA;;AAGD;AAAA;EAEC;;;AAMF;AAAA;EAEC;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;;;AAID;AAAA;AAAA;EAGC;EACA;;;AAGD;EACC;;;AAGD;EAEC;EACA;EACA;;;AAGD;EACC;EACA;EACA;;;AAGD;EACC;;;AAGD;EACC;AACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;;;AAGD;EACC;EACA;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;EACA;;;AAGD;EAEC;EACA;EACA;;;AAGD;EACC;EACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;;;AAGD;EACC;EACA;;;AAGD;AAAA;EAEC;EACA;EACA;EACA;EACA;;;AAID;AAAA;EAEC;;;AAID;EACC;;;AAID;EACC;EACA;EACA;;;AAID;EACC;EACA;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;;;AAID;AAAA;AAAA;EAGC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGD;AAAA;AAAA;EAGC;EACA;EACA;EACA;EACA;;;AAGD;AAAA;AAAA;EAGC;;;AAGD;AAAA;AAAA;EAGC;;;AAGD;EACC;;;AAGD;EACC;;;AAGD;AAAA;AAAA;EAGC;;;AAID;AAIC;EACC,YxBjfM;;AwBmfN;EACC;EACA;;AAEA;EACC;EACA,OxBjfK;;AwBqfN;EACC,OxBtfK;;AwB4fR;EACC,YxBrgBM;;AwBwgBP;AAAA;AAAA;EAGC;EACA;;AAGD;AAAA;AAAA;EAGC;;AAGD;EACC;EACA;EACA;EACA;EACA;;AAGD;EACC;EACA;EACA;EACA;EACA;;AAGD;EACC;EACA;EACA;EACA;EACA;;AAGD;EACC;EACA;;AAGD;EACC;;;AAQF;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;IACC;IACA;;EAED;IACC;IACA;;;AAIF;EACC;IACC;IACA;;EAED;IACC;IACA;;;AAIF;EACC;IACC;;EAED;IACC;;;AAIF;EACC;IACC;;EAED;IACC;;;AAIF;EACC;IACC;;EAED;IACC;;;AAIF;EACC;IACC;;EAED;IACC;;;AAIF;EACC;IACC;IACA;IACA;;EAED;IACC;IACA;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;;EAED;IACC;IACA;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;IACA;;EAED;IACC;IACA;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;IACA;;EAED;IACC;IACA;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;IACA;;EAED;IACC;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;IACA;;EAED;IACC;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;IACA;;EAED;IACC;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;IACA;;EAED;IACC;IACA;IACA;;;AAIF;EACC;IACC;IACA;IACA;;EAED;IACC;;;AAIF;EACC;IACC;IACA;IACA;;EAED;IACC;;;AAIF;EACC;IACC;;EAED;IACC;IACA;IACA;;EAED;IACC;;;AAIF;EACC;IACC;;EAED;IACC;IACA;IACA;;EAED;IACC;;;AAMF;EAEC;IACC;IACA;IACA;IACA;IACA;IACA;;EAGD;IACC;IACA;IACA;;EAGD;AAAA;IAEC;IACA;IACA;;EAGD;IACC;IACA;IACA;;EAGD;IACC;IACA;IACA;;EAGD;IACC;IACA;;EAGD;IAEC;;EAGD;IAEC;;EAGD;IACC;;EAGD;IACC;;EAGD;AAAA;IAEC;;EAGD;IACC;;EAGD;IACC;;EAKA;IACC;IACA;;EAGD;AAAA;IAEC;IACA;IACA;;EAGD;AAAA;AAAA;IAGC;;EAGD;AAAA;AAAA;IAGC;;EAGD;IACC;;EAGD;IACC;;;AAKH;EACC;IACC;IACA;;;AAIF;EACC;IACC;;;AC/8BF;EACC;EACA;;AAGA;EACC;;AAEA;EACC;EACA;EACA;EACA;EAEA;EACA;;AAEA;EATD;IAUE;;;AAGD;EACC;;AAGD;EACC;;AAMA;EADD;IAEE;;;AAED;EAJD;IAKE;;;AAED;EAPD;IAQE;IACA;;;AASF;EACC;;AACA;EAFD;IAGE;;;AAGD;EACC;EACA;EACA;;AAEA;EtBkDJ,OsBlDsB;EtBmDtB;;AAEA;EAEC,OsBvDqB;EtBwDrB;;AAED;EACC,OsB3D8B;EtB4D9B;;AAGD;EAEC,OsBjEqB;;AAElB;EACC;;AAGD;EACC;;AAEA;EACC;;AAGD;EACC;EACA;;AAGD;EACC;EACA;;AAIF;EACC;EACA;;AAGD;EACC;;AAIF;EAUC;;AAIA;EAdD;IAeE;IACA;IACA;IACA;IACA;;;AAGD;EACC;AAKA;;AAJA;EAFD;IAGE;;;AAID;EACC;;AACA;EAFD;IAGE;IACA;;;AAIF;EACC;EACA;;;ACtIP;EACC;EACA;EACA;EACA;;AAIE;EACC;;AAEA;EACC;;AAGD;EACC;;AAcA;EACC;;;AC9BN;E/BYC;EACA,WeqEW;EfpEX;EACA;E+BbA;EACA;EACA;EACA;;AjCsCA;EiC3CD;I/BkBE;;;;A+BRF;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;EACC;EACA;;AAEA;EACC;EACA;EACA;EACA;;AAEA;EACC;EACA;EACA;EACA;;AAGD;EACC;;AAEA;EACC;EACA;EACA;;AjCfH;EiCJA;IA0BE;IACA;;EAEA;IACC;;EAEA;IACC;IACA;IACA;IACA;;EAIF;IACC;IACA;IACA;IACA;IACA;;;;AAOJ;EACC;EACA;EACA;EACA;EACA;;AjCrDA;EiCgDD;IAOE;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;;AAEA;EACC;EACA;;AAGD;EACC;;;AAKH;EACC;;AjC9EA;EiC6ED;IAGE;;;;AAKF;E/B3EC;E+B6EA;EACA;;;AAGD;E/BrHC;EACA;;A+BuHA;E/BnHA;EACA,WeqEW;EfpEX;EACA;;AF4BA;EiCoFA;I/B7GC;;;A+BgHA;EACC;E/BxFF;EAqFA;E+BME;EjCzHF;;AAoBA;EiCiGC;IjClHA;;;;AiC4HF;EACC;;;AC9ID;EACC;;;ACEC;EACC;;AAGD;EACC;;AAKD;EACC;IACC;IACA;;;AAWH;EACC;EACA;EACA;;AAaA;EjCnCD;EACA;;AiCqCE;EjCjCF;EACA,WeqEW;EfpEX;EACA;EiCgCG,QA3BW;EA4BX;EACA;EACA,KlBmCc;EkBjCd;EACA;;AnCVH;EmCEE;IjC3BD;;;AFyBD;EmCEE;IAUE;IACA;;;AnCvBJ;EmCYE;IAcE;IACA;;;AnCPJ;EmCRE;IAkBE;;;AAKA;EACC;ElCzDL;EkC4DK;EACA;;AAMD;EACC,O9B3EG;E8B4EH;EAIA;EACA;;AnCrDL;EmC8CI;IAIE;;;AAMF;EACC;EACA;EACA;EACA,Y9BnFK;E8BoFL,O7BvEE;ELXP;EkCoFK;;AAIF;EACC,elBhBa;;AkBkBb;EACC,O7BjFE;ELXP;EkC8FK;;AnCrDL;EmCkDI;IAKE;IACA;;;AAUL;EACC;EACA;EACA;EACA;EACA;EACA,QAxGkB;EAyGlB;EACA;EACA;;AAEA;EACC;EACA;EACA;EACA;;AAKH;EjCpIA;EACA;EiCqIC;EACA;;AAEA;EACC;EACA;EACA,KlB9De;EfzEjB;EACA,WeqEW;EfpEX;EACA;;AF4BA;EmCqGC;IjC9HA;;;AiCwIE;EACC;;;AAUL;EACC;EACA;EACA;EACA;EACA;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;;;ACzLD;EACC;EACA;;;AAWC;EACC;EACA;;AAIF;EACC;;;ACpBF;EnBsCC;EACA;EAvCA;;AlBkCA;EqClCD;InBGE;;;AlBmDD;EqCtDD;InBME,UDgFgB;;;AjB1CjB;EqC5CD;InB0CE;;;AlBRD;EqClCD;InB6CE;;;AlBSD;EqCtDD;InBgDE;;;;AmB1CA;EACC;;AAEA;EACC;EpCwGH;;AAGA;EAYE;IoCxHA;MpCyHC,SALe;;;EAIhB;IoCxHA;MpCyHC,SALe;;;EAIhB;IoCxHA;MpCyHC,SALe;;;EAIhB;IoCxHA;MpCyHC,SALe;;;;AoC3GjB;EACC;;AAEA;EACC;EACA", "sourcesContent": ["/*!  This (!) needed to keep the comment in there, for WP\r\n\tTemplate:  d_theme_3\r\n\tTheme Name:   d_theme_3_child\r\n*/\r\n//  must be forward slashes\r\n\r\n//<editor-fold desc=\"-- vars and importers -- \">\r\n\r\n//parent then ours  this names all the colors and suchlike\r\n@import \"../../d_theme_3/css/partials/_.000-vars-values.scss\";\r\n@import 'partials/_000-localvars-values.scss';\r\n\r\n//parent then ours  this loads things up with color (etc) values\r\n@import \"../../d_theme_3/css/partials/_.000-vars.scss\";\r\n@import 'partials/_000-localvars.scss';\r\n//\r\n// Parent. Do the vars, then this:\r\n@import '../../d_theme_3/css/partials/_.000-importer.scss';\r\n\r\n//</editor-fold> -- vars and importers --\r\n\r\n//<editor-fold desc=\"-- plugin stuff first, so can be overrided -- \">\r\n//\r\n\r\n@import \"../../../mu-plugins/d_inc/d_gallery/css/partials/_d_gallery.scss\";\r\n@import '../../../mu-plugins/d_inc/d_gallery/css/partials/_glightbox.scss';\r\n\r\n@import '../../../mu-plugins/d_inc/_misc/CalendarListView/_calendarListView.scss';\r\n//</editor-fold> -- plugin stuff first, so can be overrided --\r\n\r\n// override whatever\r\n@import 'partials/_header.scss';\r\n\r\n@import 'partials/_theme.scss';\r\n@import 'partials/_headers.scss';\r\n@import 'partials/_home.scss';\r\n@import 'partials/_pages.scss';\r\n@import 'partials/_nav_wprmenu.scss';\r\n@import 'partials/_products_portfolio.scss';\r\n\r\n\r\n\r\n\r\n", "@mixin responsive-padding-01-02 {\r\n\tpadding: 0 1vw;\r\n\r\n\t@include up_to_ipad {\r\n\t\tpadding: 0 2vw;\r\n\t}\r\n}\r\n@mixin responsive-padding-01_5-02 {\r\n\tpadding: 0 1.5vw;\r\n\r\n\t@include up_to_ipad {\r\n\t\tpadding: 0 2vw;\r\n\t}\r\n}\r\n@mixin responsive-padding-01_5-02_foursides {\r\n\tpadding: 1.5vw;\r\n\r\n\t@include up_to_ipad {\r\n\t\tpadding: 2vw;\r\n\t}\r\n}\r\n\r\n\r\n// the parentheses () in a mixin definition are only required if the mixin takes arguments.\r\n// if it has () can you still call it without, if you dont need to send args?  No — if the mixin is defined with parentheses, you must include the parentheses when calling it, even if you're not passing any arguments.\r\n// &{ } refers to the parent selector, but @content already respects nesting, so wrapping it in & {} just nests it redundantly.\r\n//In fact, leaving the & block can sometimes cause unexpected selector nesting or extra specificity.\r\n//\r\n//$atMedia_ipad_max-width: 768px;\r\n//$atMedia_ipad_min-width: 769px;\r\n//$small_max-width: 550px; //ok if you do this (not in quotes) , you can add +5 to it. orwhatever\r\n//$small_min-width: 551px;\r\n//$wrapper_max-width: ($wrapperpx + $wrapperpxBuffer);\r\n//$wrapper_min-width: ($wrapperpx + $wrapperpxBuffer +1);\r\n@mixin up_to_ipad {\r\n\t@media screen and (max-width: $atMedia_ipad_max-width) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin more_than_ipad {\r\n\t@media screen and (min-width: $atMedia_ipad_min-width) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin up_to_wrapper {\r\n\t@media screen and (max-width: ($wrapperpx + $wrapperpxBuffer)) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin more_than_wrapper {\r\n\t@media screen and (min-width: ($wrapperpx + $wrapperpxBuffer) +1) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin up_to_small {\r\n\t@media only screen and (max-width: $small_max-width) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin more_than_small {\r\n\t@media screen and (min-width: $wrapper_min-width) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin up_to_max($max-width) {\r\n\t@media screen and (max-width: $max-width) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n@mixin more_than_min($min-width) {\r\n\t@media screen and (min-width: $min-width) {\r\n\t\t@content;\r\n\t}\r\n}\r\n\r\n// hiders and showers\r\n//- hide elements on everything \"iPad size\" and smaller).\r\n.hide768, .hideIpad {\r\n\t@include up_to_ipad {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n// - hide elements on everything larger than \"iPad size\").\r\n.show768, .hideLarge {\r\n\t@include more_than_ipad {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n// hide elements on everything larger than 1000px\r\n.hide-above-wrapper {\r\n\t@include more_than_wrapper {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n.show-above-wrapper {\r\n\t@include up_to_wrapper {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n", "//\r\n//\r\n// this one is more modern.  just use all px\r\n@mixin fluid-type-clamp($min-size, $max-size, $min-vw: $small_min-width, $max-vw: $wrapperpx) {\r\n\t@if unit($min-size) != px or unit($max-size) != px {\r\n\t\t@error \"Font sizes must be in px.\";\r\n\t}\r\n\t@if unit($min-vw) != px or unit($max-vw) != px {\r\n\t\t@error \"Viewport widths must be in px.\";\r\n\t}\r\n\r\n\t$slope: (strip-unit($max-size - $min-size) / strip-unit($max-vw - $min-vw)) * 100;\r\n\t$intercept: strip-unit($min-size) - ($slope * strip-unit($min-vw) / 100);\r\n\r\n\tfont-size: clamp(\r\n\t\t#{$min-size},\r\n\t\tcalc(#{$intercept}px + #{$slope}vw),\r\n\t\t#{$max-size}\r\n\t);\r\n}\r\n\r\n// old https://css-tricks.com/snippets/css/fluid-typography/\r\n@mixin fluid-type($max-font-size, $min-font-size:12px, $min-vw:550px, $max-vw:1100px) {\r\n\t@include fluid-type-clamp($min-font-size, $max-font-size, $min-vw, $max-vw);\r\n\t//$u1: unit($min-vw);\r\n\t//$u2: unit($max-vw);\r\n\t//$u3: unit($min-font-size);\r\n\t//$u4: unit($max-font-size);\r\n\t//\r\n\t//@if $u1 == $u2 and $u1 == $u3 and $u1 == $u4 {\r\n\t//\t& {\r\n\t//\t\tfont-size: $min-font-size;\r\n\t//\t\t@media screen and (min-width: $min-vw) {\r\n\t//\t\t\tfont-size: calc(#{$min-font-size} + #{strip-unit($max-font-size - $min-font-size)} * ((100vw - #{$min-vw}) / #{strip-unit($max-vw - $min-vw)}));\r\n\t//\t\t\t--devnote: \"minvw\";\r\n\t//\t\t}\r\n\t//\t\t@media screen and (min-width: $max-vw) {\r\n\t//\t\t\tfont-size: $max-font-size;\r\n\t//\t\t\t--devnote: \"maxvw\";\r\n\t//\t\t}\r\n\t//\t}\r\n\t//}\r\n}\r\n\r\n\r\n// .cards{\r\n//\t\tcontainer-type: inline-size\r\n//\t\t// optional container-name: card\r\n//  \th2 {\r\n//\t\t\t@include fluid-container-type(18px, 26px, 225px, 350px); // Min size 18px, max 26px\r\n// \t\t.subtitle {\r\n//\t\t\t@include fluid-container-type(14px, 18px, 225px, 350px); // Min size 14px, max 18px\r\n@mixin fluid-container-type(\r\n\t$min-size,\r\n\t$max-size,\r\n\t$min-breakpoint: $small_min-width,\r\n\t$max-breakpoint: $wrapper_max-width,\r\n\t$container-name: null//optional. if you send it in, it will use that name for the container query. otherwise, it will use the\r\n\t// default container query which is the nearest parent with a container-type: inline-size (or other.)\r\n) {\r\n\t// Base size (fallback)\r\n\tfont-size: $min-size;\r\n\r\n\t// Container query implementation with discrete steps\r\n\t@supports (container-type: inline-size) {\r\n\t\t$breakpoint-range: strip-unit($max-breakpoint) - strip-unit($min-breakpoint);\r\n\t\t$size-range: strip-unit($max-size) - strip-unit($min-size);\r\n\t\t$steps: (0.2, 0.4, 0.6, 0.8, 1);\r\n\r\n\t\t@each $step in $steps {\r\n\t\t\t$bp: strip-unit($min-breakpoint) + ($breakpoint-range * $step);\r\n\t\t\t$size: strip-unit($min-size) + ($size-range * $step);\r\n\r\n\t\t\t// Use max values for the final step, calculated values for others\r\n\t\t\t$final-bp: if($step == 1, strip-unit($max-breakpoint), $bp);\r\n\t\t\t$final-size: if($step == 1, $max-size, #{$size}px);\r\n\r\n\t\t\t// Generate container query with or without name\r\n\t\t\t$container-prefix: if($container-name, \"#{$container-name} \", \"\"); //spaces are important.\r\n\t\t\t@container #{$container-prefix}(min-width: #{$final-bp}px) {\r\n\t\t\t\tfont-size: $final-size;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n//You provide an array of padding values (as many as you want)\r\n//The mixin calculates evenly spaced breakpoints between your min and max container widths\r\n//Each padding value is applied at its corresponding breakpoint\r\n//\r\n//For example, if you provide:\r\n//\t\t5 padding values\r\n//\t\tMin container width of 250px\r\n// \t\tMax container width of 400px\r\n// The mixin will create container queries at:\r\n//\t\t250px (first padding value)\r\n//\t\t287.5px (second padding value)\r\n//\t\t325px (third padding value)\r\n//\t\t362.5px (fourth padding value)\r\n//\t\t400px (fifth padding value)\r\n//\r\n//You can use it with any number of padding values: The more values you provide, the more\r\n//granular control you have over how the padding changes as the container width increases.\r\n// Fluid padding based on container width using predefined steps\r\n// Define your padding values for each step\r\n// $padding-steps: (2vw 0, 3vw 0, 4vw 6px, 4vw, 6vw);\r\n// @include fluid-container-padding($padding-steps, 250px, 400px);\r\n@mixin fluid-container-padding(\r\n\t$padding-values,\r\n\t$min-breakpoint: $small_min-width,\r\n\t$max-breakpoint: $wrapper_max-width,\r\n\t$container-name: null//optional. if you send it in, it will use that name for the container query. otherwise, it will use the\r\n\t// default container query which is the nearest parent with a container-type: inline-size (or other.)\r\n) {\r\n\t// Base padding (fallback) - use the last value in the array\r\n\tpadding: nth($padding-values, length($padding-values));\r\n\r\n\t// Container query implementation with discrete steps\r\n\t@supports (container-type: inline-size) {\r\n\t\t$breakpoint-range: strip-unit($max-breakpoint) - strip-unit($min-breakpoint);\r\n\t\t$steps-count: length($padding-values);\r\n\r\n\t\t@for $i from 1 through $steps-count {\r\n\t\t\t$step-percentage: ($i - 1) / ($steps-count - 1); // \t\t\t\t\t\t\t\t// Calculate the step percentage (0 to 1)\r\n\t\t\t$bp: strip-unit($min-breakpoint) + ($breakpoint-range * $step-percentage); // // Calculate the breakpoint for this step\r\n\t\t\t$final-bp: if($i == $steps-count, strip-unit($max-breakpoint), $bp); // \t\t// Use max breakpoint for the final step\r\n\t\t\t$padding-value: nth($padding-values, $i); // \t\t\t\t\t\t\t\t    // Get the padding value for this step\r\n\r\n\t\t\t// Generate container query with or without name\r\n\t\t\t$container-prefix: if($container-name, \"#{$container-name} \", \"\");\r\n\t\t\t@container #{$container-prefix}(min-width: #{$final-bp}px) {\r\n\t\t\t\tpadding: $padding-value;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n.asdf {display: none;}\r\n\r\n", "//\r\n@import \"_mixins-links.scss\";\r\n@import \"_mixins-responsive.scss\";\r\n@import \"_mixins-type.scss\";\r\n@import \"_mixins-grid.scss\";\r\n\r\n\r\n//<editor-fold desc=\"-- container wrapper mixins -- \">\r\n@mixin container {\r\n\tmargin: 0 auto;\r\n\twidth: 100%;\r\n}\r\n\r\n@mixin wrapper {\r\n\twidth: 100%;\r\n\tmax-width: $wrapperpx;\r\n\tmargin: 0 auto;\r\n\tpadding: 0;\r\n\r\n\t@include up_to_wrapper {\r\n\t\tpadding: 0 $wrapper_padding;\r\n\t}\r\n}\r\n//</editor-fold> -- container wrapper mixins --\r\n\r\n@mixin gradientbg($bgcolor, $from, $to) {\r\n\tbackground-color: $bgcolor;\r\n\tbackground-image: -webkit-linear-gradient(top, $from, $to);\r\n\tbackground-image: -moz-linear-gradient(top, $from, $to);\r\n\tbackground-image: -ms-linear-gradient(top, $from, $to);\r\n\tbackground-image: -o-linear-gradient(top, $from, $to);\r\n\tbackground-image: linear-gradient($from, $to);\r\n}\r\n\r\n@mixin gradientbg3($bgcolor, $from, $mid, $to) {\r\n\tbackground-color: $bgcolor;\r\n\tbackground-image: -webkit-linear-gradient(top, $from, $mid, $to);\r\n\tbackground-image: -moz-linear-gradient(top, $from, $mid, $to);\r\n\tbackground-image: -ms-linear-gradient(top, $from, $mid, $to);\r\n\tbackground-image: -o-linear-gradient(top, $from, $mid, $to);\r\n\tbackground-image: linear-gradient($from, $mid, $to);\r\n}\r\n\r\n\r\n@mixin shadowtwo($one: 2px 3px 3px rgba(0,0,0, 0.3), $two: -1px -1px 3px rgba(0,0,0, 0.3)) {\r\n\tbox-shadow: $one, $two;\r\n}\r\n\r\n@mixin shadowone($one: 2px 3px 3px rgba(0,0,0, 0.3)) {\r\n\tbox-shadow: $one;\r\n}\r\n\r\n\r\n@mixin alpha60 {\r\n\tbackground: rgb(255, 255, 255); // /* Fallback for web browsers that doesn't support RGBa */\r\n\tbackground: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n@mixin alpha80 {\r\n\tbackground: rgb(255, 255, 255);\r\n\tbackground: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n\r\n// http://nicolasgallagher.com/pure-css-speech-bubbles/demo/\r\n@mixin speechbubble1 {\r\n\t&:before {\r\n\t\tborder-color: #000000 rgba(0, 0, 0, 0);\r\n\t\tborder-style: solid;\r\n\t\tborder-width: 14px 14px 0;\r\n\t\tbottom: -14px;\r\n\t\tcontent: \"\";\r\n\t\tdisplay: block;\r\n\t\tleft: 46px;\r\n\t\tposition: absolute;\r\n\t\twidth: 0;\r\n\t}\r\n\t&:after {\r\n\t\tborder-color: #FFFFFF rgba(0, 0, 0, 0);\r\n\t\tborder-style: solid;\r\n\t\tborder-width: 13px 13px 0;\r\n\t\tbottom: -13px;\r\n\t\tcontent: \"\";\r\n\t\tdisplay: block;\r\n\t\tleft: 47px;\r\n\t\tposition: absolute;\r\n\t\twidth: 0;\r\n\t}\r\n\tbackground: none repeat scroll 0 0 #FFFFFF;\r\n\tborder: 1px solid #000000;\r\n\tborder-radius: 10px;\r\n\tcolor: #333333;\r\n\tmargin: 0 auto;\r\n\tpadding: 15px;\r\n\tposition: relative;\r\n\r\n}\r\n\r\n// .rot{\t@include rotate(77);}\r\n\r\n\r\n@mixin borderbox {\r\n\t@include boxsizing(border-box);\r\n}\r\n\r\n@mixin contentbox {\r\n\t@include boxsizing(content-box);\r\n}\r\n\r\n@mixin boxsizingInherit {\r\n\t@include boxsizing(inherit);\r\n}\r\n\r\n@mixin boxsizing($type: border-box) {\r\n\t-moz-box-sizing: $type;\r\n\t-webkit-box-sizing: $type;\r\n\tbox-sizing: $type;\r\n}\r\n\r\n// back compat alias for dugs old mixin // still the thing in 2025\r\n@mixin cf {\r\n\t&:after {\r\n\t\tcontent: \"\";\r\n\t\tdisplay: table;\r\n\t\tclear: both;\r\n\t}\r\n\tclear: both;\r\n}\r\n\r\n@mixin responsive-border-radius($min: 5px, $vw: 1vw, $max: 10px) {\r\n\tborder-radius: clamp(#{$min}, #{$vw}, #{$max});\r\n}\r\n//ugh, i give up.\r\n// overflow-wrap: break-word;  seems best for h2 in a card.\r\n//@mixin text-wrap {\r\n//\t// this has to be on the acutal element, like h2 or p or .class directly containing the text\r\n//\tword-break: normal; // Don't break words mid-word\r\n//\toverflow-wrap: break-word; // Only break if absolutely necessary\r\n//\t-webkit-hyphens: auto;\r\n//\t-moz-hyphens: auto;\r\n//\thyphens: auto;\r\n//}\r\n@mixin border-radius($radius) { // this is really not needed.  pretty sure its unused\r\n\tborder-radius: $radius;\r\n}\r\n// shadows.  this mixin is not really needed.\r\n@mixin box-shadow($shadow...) {\r\n\tbox-shadow: $shadow;\r\n}\r\n\r\n// alias\r\n\r\n\r\n@mixin sass-dump($value) {\r\n\t@debug \"Type: #{type-of($value)}\";\r\n\r\n\t@if type-of($value) == \"list\" {\r\n\t\t@debug \"Length: #{length($value)}\";\r\n\t\t@debug \"Values:\";\r\n\t\t@for $i from 1 through length($value) {\r\n\t\t\t@debug \"  [#{$i}]: #{nth($value, $i)}\";\r\n\t\t}\r\n\t} @else if type-of($value) == \"map\" {\r\n\t\t@debug \"Keys:\";\r\n\t\t@each $key, $val in $value {\r\n\t\t\t@debug \"  #{$key}: #{$val}\";\r\n\t\t}\r\n\t} @else {\r\n\t\t@debug \"Value: #{$value}\";\r\n\t}\r\n}\r\n\r\n// Example usage:\r\n// $my-list: (1px, 2px, 3px);\r\n// @include sass-dump($my-list);\r\n\r\n// yes, we use this for beaver builder buttons, etc.\r\n//https://gist.github.com/ryanturner10/d29b7d1728274e5b79f5c6a0692e0496\r\n//@function strip-unit($number) {\r\n//\t@return $number / ($number * 0 + 1);\r\n//}\r\n// Helper function to strip units\r\n@function strip-unit($number) {\r\n\t@if type-of($number) == 'number' and not unitless($number) {\r\n\t\t@return $number / ($number * 0 + 1);\r\n\t}\r\n\t@return $number;\r\n}\r\n\r\n.asdf {display: none;}\r\n", "// cant do *{position relative}... it just doesnt work with jquery stuff.\r\n\r\nhtml {\r\n\t-moz-box-sizing: border-box;\r\n\t-webkit-box-sizing: border-box;\r\n\tbox-sizing: border-box;\r\n\tpadding: 0 !important; // for wprm but yeah.\r\n\tmargin: 0 !important; // for wprm but yeah.\r\n\t\r\n}\r\n\r\n*, *:before, *:after {\r\n\t//@include box-sizing(inherit);//http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/\r\n\t-moz-box-sizing: border-box;\r\n\t-webkit-box-sizing: border-box;\r\n\tbox-sizing: border-box; //input boxes dont inherit, maybe...\r\n}\r\n\r\n// .notmobile, .ismobile, .not_mobile, .is_mobile {}\r\n\r\n// this is set by the d_misc plugin, but it's unused for a long time.\r\n//.honey, .honeypot {\r\n//\tvisibility: hidden !important;\r\n//\tdisplay: none !important;\r\n//}\r\n\r\n.displaynone {\r\n\tdisplay: none;\r\n}\r\n\r\n.displaynoneimportant {\r\n\tdisplay: none !important;\r\n}\r\n\r\n\r\ntable td {\r\n\tvertical-align: top;\r\n}\r\n\r\n\r\n.clearfix, .cf {\r\n\t@include cf;\r\n}\r\n\r\n\r\n.redux-container, .redux-container *,\r\n#redux-sub-footer, #redux-sub-footer * {\r\n\t@include contentbox;\r\n}\r\n\r\na, a:focus, a:active, a:hover, object, embed, input::-moz-focus-inner { // https://css-tricks.com/removing-the-dotted-outline// /Because that outline was providing an important accessibility feature, you should really (really) consider adding back in a style for your links focus and active states. Personally, I just like to make them the same as the hover state. It's about the same thing, as far as actual function. Whatever your hover state is, even if it's shifting a background image or changing size or whatever, it can be joined with the active and focus states. Like so:  //a:hover, a:active, a:focus {  styling for any way a link is about to be used\r\n\toutline: 0;\r\n\tbackground: transparent;\r\n}\r\n\r\n\r\nimg {\r\n\tborder: none;\r\n\tmax-width: 100%;\r\n\theight: auto;\r\n\tvertical-align: bottom; //get rid of space below.  better than doing display block.\r\n}\r\n\r\n// dont work on flex or float\r\n.d-vertical-center-transform-outer {\r\n\theight: 100%;\r\n\tposition: relative;\r\n\r\n\t.d-vertical-center-transform-inner {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t}\r\n}\r\n\r\n\r\n// idk about this. usually dont work on flex or float\r\n.d-vertical-center-table-outer {\r\n\tdisplay: table;\r\n\theight: 100%;\r\n\tbackground: green;\r\n\r\n\t.d-vertical-center-table-inner {\r\n\t\tdisplay: table-cell;\r\n\t\tvertical-align: middle;\r\n\t\tbackground: pink;\r\n\t}\r\n}\r\n\r\n// yeah, i guess you gotta go flex in a flex, flexception.\r\n.d-vertical-center-flex-outer-container {\r\n\tdisplay: flex;\r\n\r\n\t.d-vertical-center-flex-outer {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\r\n\t\t.d-vertical-center-flex-inner {\r\n\t\t\t// this is the one that is centered\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n\r\n", "// normalize.css 2012-07-07 http://github.com/necolas/normalize.css with lots of changes dug\r\n\r\nhtml {\r\n\tfont-family: $font_normal; //  Set default font family to sans-serif.\r\n\t-ms-text-size-adjust: 100%; //Prevent iOS text size adjust after orientation change, without disabling user zoom.\r\n\t-webkit-text-size-adjust: 100%;\r\n\tfont-size: 16px;\r\n}\r\n\r\n$bodyLineHeight: 1.5 !default;\r\nbody {\r\n\twidth: 100%;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\r\n\tbackground-color: $bodyBGcolor;\r\n\tbackground-image: none;\r\n\tbackground-size: cover;\r\n\tbackground-attachment: fixed;\r\n\r\n\tfont-family: $font_normal;\r\n\tline-height: $bodyLineHeight;\r\n\tcolor: $fontcolor;\r\n\tfont-weight: normal;\r\n\r\n\t-webkit-font-smoothing: antialiased;\r\n\t-moz-osx-font-smoothing: grayscale;\r\n\r\n}\r\n\r\narticle, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {\r\n\tdisplay: block;\r\n}\r\n\r\naudio, canvas, video {\r\n\tdisplay: inline-block;\r\n}\r\n\r\naudio:not([controls]) {\r\n\tdisplay: none;\r\n\theight: 0;\r\n}\r\n\r\n// Prevent modern browsers from displaying `audio` without controls. * Remove excess height in iOS 5 devices. */\r\n[hidden], template {\r\n\tdisplay: none;\r\n}\r\n\r\n//Address `[hidden]` styling not present in IE 8/9. * Hide the `template` element in IE, Safari, and Firefox < 22.*/\r\nu {\r\n\ttext-decoration: underline;\r\n}\r\n\r\na {\r\n\tbackground: transparent;\r\n\tbackground-color: transparent;\r\n}\r\n\r\n// Remove the gray background color from active links in IE 10.\r\na:focus, a:active, a:hover {\r\n\toutline: 0;\r\n}\r\n\r\n\r\n// Improve readability when focused and also mouse hovered in all browsers.\r\nb, strong, .strong {\r\n\tfont-weight: bold;\r\n}\r\n\r\ndfn, em, .em {\r\n\tfont-style: italic;\r\n}\r\n\r\nabbr[title] {\r\n\tborder-bottom: 1px dotted;\r\n}\r\n\r\nfigure {\r\n\tmargin: 0;\r\n}\r\n\r\nhr {\r\n\theight: 0;\r\n\tmargin: 1% 0;\r\n}\r\n\r\nhr.solid {\r\n\tborder: none !important;\r\n\tcolor: #000;\r\n\tbackground: #000;\r\n}\r\n\r\n//proper formatting (http://blog.fontdeck.com/post/9037028497/hyphens)\r\np {\r\n\tword-break: normal; /* Let the browser handle word wrapping */\r\n\toverflow-wrap: break-word; /* Ensures long words wrap properly */\r\n\thyphens: auto; /* Adds hyphens where appropriate */\r\n\r\n\t// this is old shit.\r\n\t//-ms-word-wrap: break-word;\r\n\t//word-break: break-word; //non-standart css for webkit\r\n\t//word-wrap: break-word;\r\n\t//\r\n\t//// cleaning up the ragged lines and breaks\r\n\t//-webkit-hyphens: auto;\r\n\t//-epub-hyphens: auto;\r\n\t//-moz-hyphens: auto;\r\n\t//hyphens: auto;\r\n\t//// sets a minimum number of characters before and after the break\r\n\t//-webkit-hyphenate-before: 2;\r\n\t//-webkit-hyphenate-after: 3;\r\n\t//hyphenate-lines: 3;\r\n\r\n\t// enabling fancy ligatures when available  nah, dont do that.  it makes it stupid.\r\n\t//  -webkit-font-feature-settings: \"liga\", \"dlig\";\r\n\t//   -moz-font-feature-settings: \"liga=1, dlig=1\";\r\n\t//      -ms-font-feature-settings: \"liga\", \"dlig\";\r\n\t//       -o-font-feature-settings: \"liga\", \"dlig\";\r\n\t//          font-feature-settings: \"liga\", \"dlig\";\r\n}\r\n\r\n\r\ncode, kbd, pre, samp {\r\n\tfont-family: monospace, sans-serif;\r\n\tfont-size: 1em;\r\n}\r\n\r\npre {\r\n\twhite-space: pre-wrap;\r\n}\r\n\r\nq {\r\n\tquotes: \"\\201C\" \"\\201D\" \"\\2018\" \"\\2019\"; // Set consistent quote types.\r\n}\r\n\r\nq:before, q:after {\r\n\tcontent: '';\r\n\tcontent: none; //Address inconsistent and variable font size in all browsers.\r\n}\r\n\r\nsmall, .small {\r\n\tfont-size: 75%;\r\n}\r\n\r\nsub, sup {\r\n\tfont-size: 75%;\r\n\tline-height: 0;\r\n\tposition: relative;\r\n\tvertical-align: baseline; // Prevent `sub` and `sup` affecting `line-height` in all browsers.\r\n}\r\n\r\nsup {\r\n\ttop: -0.5em;\r\n}\r\n\r\nsub {\r\n\tbottom: -0.25em;\r\n}\r\n\r\nol, ul {\r\n\tpadding: 0 0 0 40px;\r\n}\r\n\r\nul {\r\n\tlist-style: disc;\r\n\tlist-style-type: disc;\r\n\r\n\tul {\r\n\t\tlist-style: circle;\r\n\t\tlist-style-type: circle;\r\n\t}\r\n}\r\n\r\nul.unstyled, ol.unstyled,\r\nnav ul, nav ol,\r\n.nav ul, .nav ol {\r\n\tlist-style: none;\r\n\tlist-style-image: none;\r\n\tpadding: 0;\r\n\tmargin: 0;\r\n}\r\n\r\n\r\nsvg:not(:root) {\r\n\toverflow: hidden; // Correct overflow displayed oddly in IE 9.\r\n}\r\n\r\n\r\n// * ==========================================================================\r\n//   Forms\r\n\r\nfieldset {\r\n\tborder: 1px solid #c0c0c0;\r\n\tmargin: 0 2px;\r\n\tpadding: 0.35em 0.625em 0.75em;\r\n}\r\n\r\nlegend {\r\n\tborder: 0; // 1. Correct `color` not being inherited in IE 8/9.\r\n\tpadding: 0; // 2. Remove padding so people aren't caught out if they zero out fieldsets.\r\n}\r\n\r\nbutton, input, select, textarea {\r\n\tfont-family: inherit; //1. Correct font family not being inherited in all browsers.\r\n\tfont-size: 100%; //Correct font size not being inherited in all browsers.\r\n\tmargin: 0; //Address margins set differently in Firefox 4+, Safari 5, and Chrome.\r\n}\r\n\r\nbutton, input {\r\n\tline-height: normal;\r\n}\r\n\r\n//Firefox 4+\r\nbutton, select {\r\n\ttext-transform: none;\r\n}\r\n\r\n// Modern button and form control reset\r\nbutton,\r\n[type=\"button\"],\r\n[type=\"reset\"],\r\n[type=\"submit\"] {\r\n\t-webkit-appearance: button;\r\n\tcursor: pointer;\r\n\r\n\t// Remove the inner border and padding in Firefox\r\n\t&::-moz-focus-inner {\r\n\t\tborder-style: none;\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t// Correct the inability to style clickable types in iOS and Safari\r\n\t-webkit-appearance: button;\r\n\r\n\t&:focus {\r\n\t\t// For browsers that support focus-visible\r\n\t\t&:focus-visible {\r\n\t\t\toutline: 2px solid Highlight; // Edge\r\n\t\t\toutline: 5px auto -webkit-focus-ring-color; // WebKit/Blink\r\n\t\t\toutline-offset: 2px;\r\n\t\t}\r\n\r\n\t\t// Fallback for older browsers\r\n\t\t&:focus:not(:focus-visible) {\r\n\t\t\toutline: none;\r\n\t\t}\r\n\t}\r\n\r\n\t// Disabled state\r\n\t&:disabled {\r\n\t\tcursor: not-allowed;\r\n\t\topacity: 0.7;\r\n\t}\r\n}\r\n\r\n\r\n// Re-set default cursor for disabled elements.\r\ninput[type=\"checkbox\"], input[type=\"radio\"] {\r\n\tpadding: 0;\r\n}\r\n\r\n//  Remove excess padding in IE 8/9/10.\r\ninput[type=\"search\"] {\r\n\t-webkit-appearance: textfield;\r\n}\r\n\r\n//Address `appearance` set to `searchfield` in Safari 5 and Chrome.\r\ninput[type=\"search\"]::-webkit-search-cancel-button, input[type=\"search\"]::-webkit-search-decoration {\r\n\t-webkit-appearance: none;\r\n}\r\n\r\n//Remove inner padding and search cancel button in Safari 5 and Chrome on OS X.\r\n\r\n\r\nbutton::-moz-focus-inner,\r\ninput::-moz-focus-inner {\r\n\tborder: 0;\r\n\tpadding: 0; //Remove inner padding and border in Firefox 4+.\r\n}\r\n\r\ntextarea {\r\n\toverflow: auto; //Remove default vertical scrollbar in IE 8/9.\r\n\tvertical-align: top;\r\n}\r\n\r\n\r\ntable {\r\n\tborder-collapse: collapse; //Remove most spacing between table cells.\r\n\tborder-spacing: 0;\r\n}\r\n\r\n\r\n\r\n\r\n", "//\r\n//\r\n$H1_multiplier: .9;\r\n\r\n$sans: \"Quicksand\", 'Arial', 'Helvetica', 'Liberation Sans', Tahoma, Geneva, sans-serif;\r\n$serif: \"Times New Roman\", Times, serif;\r\n\r\n$ltblue: #58bcb0;\r\n$medblue: #4651F0;\r\n$blue: #4651F0;\r\n$dkblue: #251b8b;\r\n$dkblue2: #2D21A7; //gradient\r\n$dkpurple: #0F093E;\r\n\r\n$black: rgb(13, 10, 32); //hint of blue\r\n\r\n\r\n$verydarkgreengrey: #273C3A;\r\n$teal: #187177;\r\n$palepink: #ECDCD3;\r\n$red: #C34436; //bright normalish red\r\n$brick: #762D24;\r\n\r\n\r\n\r\n", "//\r\n//\r\n$verydarkgreengrey: #273C3A;\r\n$teal: #187177;\r\n$red: #f2321d; //bright normalish red\r\n$redSalmon: #ee9990;\r\n$pink: #EE9990;\r\n$brick: #762D24;\r\n$palepink: #ECDCD3;\r\n\r\n\r\n$ltblue: #58bcb0;\r\n$medblue: #395773;\r\n$blue: $medblue;\r\n$dkblue: #151d38;\r\n$aqua: #58BCB0;\r\n$dkpurple: #24154A;\r\n\r\n$green: #7dd667;\r\n$palegreen: #D9EEDB;\r\n\r\n$yellow: #ffe062; //kdug headers\r\n$yellowBright: #FCC200;\r\n$tan: #d2b48c;\r\n\r\n$white: #fff;\r\n$dkwhite: #F2F2F2;\r\n\r\n$barelygrey: #eee;\r\n$ltgrey: #ddd;\r\n$grey: #ccc;\r\n$medgrey: #aaa;\r\n$moregrey: #999;\r\n$dkgrey: #403f3f;\r\n$black: #000000;\r\n\r\n\r\n$sans: 'Arial', 'Helvetica', 'Liberation Sans', Tahoma, Geneva, sans-serif;\r\n$serif: Times, 'Times New Roman', \"Liberation Serif\", Georgia, serif;\r\n$mono: Consolas, \"Liberation Mono\", \"Courier New\", \"Lucida Console\", Courier, monospace;\r\n$narrow: 'PT Sans Narrow', \"Helvetica Narrow\", \"Arial Narrow\", Tahoma, Arial, Helvetica, sans-serif;\r\n// https://css-tricks.com/snippets/css/font-stacks/\r\n\r\n$alert-red: #FF0000;\r\n$form_red: #FF0000;\r\n$form_error_bg: #996666;\r\n//background: none repeat scroll 0 0 #996666; //$alert-red\r\n//border: 1px solid #FF0000;\r\n\r\n$font_header_weight: 900;\r\n$font_nav_size: 15px;\r\n$nav_text_transform: none;\r\n\r\n// Individual heading sizes with scaled margins\r\n// Header size multiplier (1 = 100% of default size, 0.9 = 90% of default size, etc.)\r\n$H1_multiplier: 1;\r\n\r\n$H1_topAlign: -0.18em;\r\n$H1_margin-bottom: 0.6em;\r\n$H1_line-height: 1.1;\r\n\r\n$h1_ems: 2.3em;\r\n$h2_ems: 1.75em;\r\n$h3_ems: 1.5em;\r\n$h4_ems: 1.23em;\r\n$h5_ems: 1em;\r\n$h6_ems: 0.9em;\r\n\r\n\r\n", " @mixin d_flex_grid {\r\n\t//@include display-flex;  no do it by hand:\r\n\t\t\t\t\t\t\t\t/* https://css-tricks.com/using-flexbox/ : */\r\n\tdisplay: -webkit-box;      /* OLD - iOS 6-, Safari 3.1-6 */\r\n\tdisplay: -moz-box;         /* OLD - Firefox 19- (buggy but mostly works) */\r\n\tdisplay: -ms-flexbox;      /* TWEENER - IE 10 */\r\n\tdisplay: -webkit-flex;     /* NEW - Chrome */\r\n\tdisplay: flex;             /* NEW, Spec - Opera 12.1, Firefox 20+ */\r\n\t\t// http://stackoverflow.com/questions/37150533/how-to-compass-flexbox-make-it-output-all-the-legacy-versions-and-prefixes/39208946#39208946\r\n\t\t//\tThis should give you what you want (Compass >= 1):\r\n\t\t//\t.d_flex_grid {\r\n\t\t//\t  @include flexbox((display: box), $version: 1);\r\n\t\t//\t  @include flexbox((display: flexbox), $version: 2);\r\n\t\t//\t  @include display-flex;\r\n\t\t//\t}\r\n\t\t//\r\n\t\t//\tOutput:\r\n\t\t//\t.d_flex_grid {\r\n\t\t//\t  display: -moz-box;\r\n\t\t//\t  display: -webkit-box;\r\n\t\t//\t  display: -ms-flexbox;\r\n\t\t//\t  display: -webkit-flex;\r\n\t\t//\t  display: flex;\r\n\t\t//\t}\r\n\t\tmax-width: 100%;\r\n }\r\n\r\n.d_flex_grid{\r\n\t@include d_flex_grid ;\r\n}\r\n\r\n.centering_grid{\r\n\tdisplay: -webkit-box;      /* OLD - iOS 6-, Safari 3.1-6 */\r\n\tdisplay: -moz-box;         /* OLD - Firefox 19- (buggy but mostly works) */\r\n\tdisplay: -ms-flexbox;      /* TWEENER - IE 10 */\r\n\tdisplay: -webkit-flex;     /* NEW - Chrome */\r\n\tdisplay: flex;             /* NEW, Spec - Opera 12.1, Firefox 20+ */\r\n\r\n    flex-direction: column;  /* make main axis vertical */\r\n    justify-content: center; /* center items vertically, in this case */\r\n    align-items: center;     /* center items horizontally, in this case */\r\n //   height: 300px;\r\n}\r\n\r\n.horizcenter{ // add this to the flex container.  might work for vert, too.   http://stackoverflow.com/questions/19026884/flexbox-center-horizontally-and-vertically/33049198#33049198\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n}\r\n.vertcenter{ // add this to the flex container.  might work for vert, too.   http://stackoverflow.com/questions/19026884/flexbox-center-horizontally-and-vertically/33049198#33049198\r\n\t//unused\r\n\t//place-items: center;\r\n\t-webkit-box-align: center;\r\n\t-webkit-align-items: center;\r\n\t-ms-flex-align: center;\r\n\talign-items: center;\r\n}\r\n//untried  https://meowni.ca/posts/monica-dot-css/\r\n//.horizontal {display: flex; flex-direction: row; justify-content: space-between}\r\n//.vertical {display: flex; flex-direction: column}\r\n//.center {justify-content: center; align-items: center}\r\n\r\n\r\n.d_flex_grid .df_col{\r\n\t-webkit-box-flex: 1 1 0px;      /* x OLD - iOS 6-, Safari 3.1-6 */ /* needs to have the 0px on the end.  0 dont work, for IE. */\r\n\t-moz-box-flex: 1 1 0px;         /* OLD - Firefox 19- */\r\n\t//  width: 20%;               For old syntax, otherwise collapses.\r\n\t-webkit-flex: 1 1 0px;          /* Chrome */\r\n\t-ms-flex: 1 1 0px;              /* IE 10 */\r\n\tflex: 1 1 0px;                  /* NEW, Spec - Opera 12.1, Firefox 20+ */\r\n\tpadding-right:2%;\r\n\tflex-basis: auto; // i think this overrides the last value of -flex: 1 1 0px-  not sure what this really means. https://stackoverflow.com/questions/********/flex-flow-not-working-in-ie11\r\n}\r\n\r\n .d_flex_grid .shrinktofit{\r\n\tflex: 0 0 0;\r\n}\r\n\r\n.d_flex_grid .df_col.halfof3{\r\n\tflex: 0 0 50%;/* have to account for margins i guess */\r\n\twidth:50%;\r\n}\r\n\r\n.d_flex_grid .df_col.sidebar320{\r\n\tflex: 0 0 320px;\r\n}\r\n.d_flex_grid .df_col.sidebar200{\r\n\tflex: 0 0 200px;\r\n}\r\n\r\n\r\n\r\n.d_flex_grid.two_one{\r\n\tflex-wrap: wrap;\r\n\tjustify-content: flex-start;\r\n\t.df_col{\r\n\t\twidth:49%;\r\n\t\tmax-width:49%;\r\n\t}\r\n\t .df_col:nth-child(2n+0) {\r\n\t\tpadding-right:0;\r\n\t}\r\n}\r\n@media (max-width: 768px) {\r\n\t.d_flex_grid.two_one{\r\n\t\t.df_col {\r\n\t\t\twidth:100%;\r\n\t\t\tmax-width:100%;\r\n\t\t\tmargin-bottom: 2vw;\t\t\t// % dont work on flex items, at least not like that. /\r\n\t\t\tpadding-right:2%;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.d_flex_grid.five_three_two{\r\n\tflex-wrap: wrap;\r\n\tjustify-content: flex-start;\r\n\t.df_col{\r\n\t\twidth:20%;\r\n\t\tmax-width:20%;\r\n\t\tmargin-bottom: 2vw;\r\n\t}\r\n\t .df_col:nth-child(5n+0) {\r\n\t\tpadding-right:0;\r\n\t}\r\n}\r\n@media (max-width: 768px) {\r\n\t.d_flex_grid.five_three_two{\r\n\t\t.df_col {\r\n\t\t\twidth:32%;\r\n\t\t\tmax-width:32%;\r\n\t\t\tmargin-bottom: 2vw;\t\t\t// % dont work on flex items, at least not like that. /\r\n\t\t}\r\n\t\t.df_col:nth-child(5n+0) {\r\n\t\t\tpadding-right:2%;\r\n\t\t}\r\n\t\t.df_col:nth-child(3n+0) {\r\n\t\t\tpadding-right:0%;\r\n\t\t}\r\n\t}\r\n}\r\n@media (max-width: 500px) {\r\n\t.d_flex_grid.five_three_two{\r\n\t\t.df_col {\r\n\t\t\twidth:50%;\r\n\t\t\tmax-width:50%;\r\n\t\t\tmargin-bottom: 2vw;\t\t\t// % dont work on flex items, at least not like that. /\r\n\t\t}\r\n\t\t.df_col:nth-child(5n+0) {\r\n\t\t\tpadding-right:0%;\r\n\t\t}\r\n\t\t.df_col:nth-child(3n+0) {\r\n\t\t\tpadding-right:0%;\r\n\t\t}\r\n\t\t.df_col:nth-child(1n+0) {\r\n\t\t\tpadding-right:2%;\r\n\t\t}\r\n\t\t.df_col:nth-child(2n+0) {\r\n\t\t\tpadding-right:0%;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n\r\n\r\n\r\n.d_flex_grid.four_two_one{\r\n\twidth:25%;\r\n\tmax-width:25%;\r\n\tflex-wrap: wrap;\r\n\tjustify-content: space-between;\r\n}\r\n@media (max-width: 768px) {\r\n\t.d_flex_grid.four_two_one .df_col {\r\n\t\tflex: 1 1 48%;\r\n\t\tmargin-bottom: 2vw;// % dont work on flex items, at least not like that. /\r\n\t\tpadding-right:0 !important;// has to override the subsequent media querys\r\n\t}\r\n\t.d_flex_grid.four_two_one .df_col:nth-child(1),\r\n\t.d_flex_grid.four_two_one .df_col:nth-child(3) {\r\n\t\tpadding-right:2%;\r\n\t}\r\n }\r\n @media (max-width: 600px) {\r\n\t .d_flex_grid.four_two_one{\r\n\t\t display:block;// just take it off flex so the cols act like normal divs /\r\n\t }\r\n\t .d_flex_grid.four_two_one .df_col{\r\n\t\t padding-right:0;\r\n\t\t margin-bottom: 2% ;// % dont work on flex items, at least not like that. but these are now regular cols, cuz display block /\r\n\t }\r\n }\r\n/* */\r\n", "//LINK STYLES\r\na {\r\n\t@include links($linkcolor, $linkover, linkDecNo, linkDecUnderline);\r\n}\r\na:link { // this is prolly crap\r\n\t-webkit-tap-highlight-color: rgba(0, 0, 0, 0.3); // mobile tap color this highlights links on iPhones/iPads.\t\tso it basically works like the :hover selector\t\tfor mobile devices.\r\n}\r\n\r\na.underline_no_yes {\r\n\t&, &:link, &:visited, &:active {\r\n\t\t@include linkstyles(linkDecNo);\r\n\t}\r\n\r\n\t&:focus, &:hover {\r\n\t\t@include linkstyles(linkDecUnderline);\r\n\t}\r\n}\r\na.underline_no_no {\r\n\t&, &:link, &:visited, &:active {\r\n\t\t@include linkstyles(linkDecNo);\r\n\t}\r\n\r\n\t&:focus, &:hover {\r\n\t\t@include linkstyles(linkDecNo);\r\n\t}\r\n}\r\n\r\n/* xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx   */\r\n\r\n\r\n.capitalized {\r\n\ttext-transform: capitalize;\r\n}\r\n\r\n.alignleft {\r\n\ttext-align: left;\r\n}\r\n\r\n.aligncenter {\r\n\ttext-align: center;\r\n}\r\n\r\n.alignright {\r\n\ttext-align: right;\r\n\r\n}\r\n\r\n\r\n.darkbg {\r\n\tbackground-color: #000000;\r\n\tcolor: $fontColorDkBg;\r\n\r\n\ta {\r\n\t\t@include links($linkcolorDkBg !important, $linkoverDkBg !important, linkDecNo, linkDecUnderline); // use important in quotes 'linkDecUnderline !important'\r\n\t}\r\n}\r\n\r\n#content, .content {\r\n\tp {\r\n\t\tmargin: 0 0 1.5em;\r\n\r\n\t\ta {\r\n\t\t\ttext-decoration: underline; // the color is elsewhere.\r\n\r\n\t\t\t&:visited {\r\n\t\t\t\ttext-decoration: underline;\r\n\t\t\t}\r\n\r\n\t\t\t&:hover, &:focus, &:active {\r\n\t\t\t\ttext-decoration: none;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\timg.alignleft {\r\n\t\tmargin-right: 1.5em;\r\n\t\tdisplay: inline;\r\n\t\tfloat: left;\r\n\t}\r\n\r\n\r\n\timg.alignright {\r\n\t\tmargin-left: 1.5em;\r\n\t\tdisplay: inline;\r\n\t\tfloat: right;\r\n\r\n\t}\r\n\r\n\r\n\timg.aligncenter {\r\n\t\tmargin-right: auto;\r\n\t\tmargin-left: auto;\r\n\t\tdisplay: block;\r\n\t\tclear: both;\r\n\t}\r\n\r\n\r\n\timg.alignleft,\r\n\timg.alignright,\r\n\timg.aligncenter {\r\n\t\t@media screen and (max-width: 1000px) {\r\n\t\t\t& {\r\n\t\t\t\tmax-width: 75%;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@media screen and (max-width: 768px) {\r\n\t\t\t& {\r\n\t\t\t\tmax-width: 50%;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@media screen and (max-width: 600px) {\r\n\t\t\t& {\r\n\t\t\t\tmax-width: 100%;\r\n\t\t\t\tfloat: none;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\tblockquote {\r\n\t\tmargin: 0 0 1.5em 0.75em;\r\n\t\tpadding: 0 0 0 0.75em;\r\n\t\tborder-left: 3px solid $blue;\r\n\t\tfont-style: italic;\r\n\t\tcolor: $moregrey;\r\n\r\n\t\t&:before {\r\n\t\t}\r\n\t}\r\n\r\n\tdl, dt, dd {\r\n\t\tmargin-left: 0;\r\n\t\tfont-size: 0.9em;\r\n\t\tcolor: #787878;\r\n\t\tmargin-bottom: 1.5em;\r\n\t}\r\n}\r\n\r\n\r\n.dashicons, .dashicons-before::before, .dashicons-before::after, a:has(.dashicons) {\r\n\t//this seems to work. leave all this shit to try to get rid of underlines under icons\r\n\ttext-decoration: none !important;\r\n\ttext-decoration-color: $bodyBGcolor;\r\n\r\n\ta {\r\n\t\t@include links($linkcolor, $linkover !important, linkDecNo, linkDecNo); // use important in quotes 'linkDecUnderline !important'\r\n\t}\r\n\r\n\t@include links($linkcolor, $linkover, linkDecNo, linkDecNo); // use important in quotes 'linkDecUnderline !important'\r\n}\r\n\r\n.byline {\r\n\tcolor: $ltgrey;\r\n\tfont-style: italic;\r\n\tmargin: 0;\r\n}\r\n\r\n.wp-caption {\r\n\tmax-width: 100%;\r\n\tbackground: #eee;\r\n\tpadding: 5px;\r\n\r\n\timg {\r\n\t\tmax-width: 100%;\r\n\t\tmargin-bottom: 0;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\tp.wp-caption-text {\r\n\t\tfont-size: 0.85em;\r\n\t\tmargin: 4px 0 7px;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n}\r\n\r\n\r\n", "//\r\n//<editor-fold desc=\"--  im not sure if this shit is used. -- \">\r\n\r\n@mixin linkcolors($normal, $hover: 0, $active: 0, $visited: 0, $focus: 0) {\r\n\tcolor: $normal;\r\n\t@if $hover   == 0 {\r\n\t\t$hover: $normal;\r\n\t}\r\n\t@if $active  == 0 {\r\n\t\t$active: $normal;\r\n\t}\r\n\t@if $visited == 0 {\r\n\t\t$visited: $normal;\r\n\t}\r\n\t@if $focus   == 0 {\r\n\t\t$focus: $normal;\r\n\t}\r\n\r\n\t&:link {\r\n\t\tcolor: $normal;\r\n\t}\r\n\t&:visited {\r\n\t\tcolor: $visited;\r\n\t}\r\n\t&:focus {\r\n\t\tcolor: $focus;\r\n\t}\r\n\t&:hover {\r\n\t\tcolor: $hover;\r\n\t}\r\n\t&:active {\r\n\t\tcolor: $active;\r\n\t}\r\n}\r\n\r\n@mixin linkDecNo {\r\n\ttext-decoration: none;\r\n}\r\n\r\n@mixin linkDecUnderline {\r\n\ttext-decoration: underline;\r\n}\r\n\r\n@mixin linkDecNoImportant {\r\n\ttext-decoration: none !important;\r\n}\r\n\r\n@mixin linkDecUnderlineImportant {\r\n\ttext-decoration: underline !important;\r\n}\r\n\r\n// use important in quotes 'linkDecUnderline !important'\r\n\r\n@mixin linkstyles($style) {\r\n\t@if $style == linkDecNo {\r\n\t\t@include linkDecNo;\r\n\t}\r\n\t@if $style == linkDecUnderline {\r\n\t\t@include linkDecUnderline;\r\n\t}\r\n\t@if $style == 'linkDecNo !important' {\r\n\t\t@include linkDecNoImportant;\r\n\t}\r\n\t@if $style ==  linkDecNoImportant {\r\n\t\t@include linkDecNoImportant;\r\n\t}\r\n\t@if $style == 'linkDecUnderline !important' {\r\n\t\t@include linkDecUnderlineImportant;\r\n\t}\r\n\t@if $style == linkDecUnderlineImportant {\r\n\t\t@include linkDecUnderlineImportant;\r\n\t}\r\n\t// use important in quotes 'linkDecUnderline !important'\r\n}\r\n//'linkDecNo !important', linkDecNoImportant  either works but you have to quote if using linkDecNo !important\r\n//@mixin links($normalcolor: blue, $hovercolor: purple, $normalstyle: linkDecNo, $hoverstyle: linkDecUnderline) { // use important in quotes 'linkDecUnderline !important'\r\n//\t//@include linkcolors($normalcolor, $hovercolor);\r\n//\t&, &:link, &:visited, &:active, &:focus {\r\n//\t\t@include linkstyles($normalstyle);\r\n//\t}\r\n//\t&:hover {\r\n//\t\t@include linkstyles($hoverstyle);\r\n//\t}\r\n//}\r\n\r\n//</editor-fold> --  im not sure if this shit is used. --\r\n\r\n\r\n@mixin links(////how to use...\r\n\t//// .somediv a{\r\n\t////\t  @include links(red, blue,  linkDecNo, linkDecUnderline);\r\n\t////\t}\r\n\t$normalcolor: $linkcolor,\r\n\t$hovercolor: $linkover,\r\n\t$normalstyle: 'linkDecNo',\r\n\t$hoverstyle: 'linkDecUnderline',\r\n\t$backgroundcolor: null) {\r\n\r\n\t$styles: (\r\n\t\t'linkDecNo': none,\r\n\t\t'linkDecNo !important': none !important,\r\n\t\t'linkDecNoImportant': none !important,\r\n\t\t'linkDecUnderline': underline,\r\n\t\t'linkDecUnderline !important': underline !important,\r\n\t\t'linkDecUnderlineImportant': underline !important,\r\n\t\t// i think we only need to do this stupid shit so that you can send in the normal values like a normal person.\r\n\t\t'none': none,\r\n\t\t'none !important': none !important,\r\n\t\t'underline': underline,\r\n\t\t'underline !important': underline !important\r\n\t);\r\n\r\n\tcolor: $normalcolor;\r\n\ttext-decoration: map-get($styles, $normalstyle);\r\n\r\n\t&:link,\r\n\t&:visited {\r\n\t\tcolor: $normalcolor;\r\n\t\ttext-decoration: map-get($styles, $normalstyle);\r\n\t}\r\n\t&:hover {\r\n\t\tcolor: $hovercolor;\r\n\t\ttext-decoration: map-get($styles, $hoverstyle);\r\n\t}\r\n\r\n\t&:focus,\r\n\t&:active {\r\n\t\tcolor: $normalcolor;\r\n\t\t//\ttext-decoration: none;  dont work:  https://stackoverflow.com/questions/10471929/underlining-visited-links\r\n\t\t@if $backgroundcolor {\r\n\t\t\ttext-decoration-color: $backgroundcolor; // this is what you gotta do to hide it.\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// Mixin for heading styles\r\n@mixin headers(\r\n\t$color: $H1_color,\r\n\t$link-color:$H1_link,\r\n\t$link-color-over: $H1_link_over,\r\n\t$link-decoration: none,\r\n\t$link-decoration-over: none,\r\n\t$backgroundcolor: null\r\n) {\r\n\th1, .h1, .H1,\r\n\th2, .h2, .H2,\r\n\th3, .h3, .H3,\r\n\th4, .h4, .H4,\r\n\th5, .h5, .H5,\r\n\th6, .h6, .H6, .headerfont {\r\n\t\tcolor: $color;\r\n\r\n\t\ta {\r\n\t\t\t@include links(\r\n\t\t\t\t$normalcolor: $link-color,\r\n\t\t\t\t$hovercolor: $link-color-over,\r\n\t\t\t\t$normalstyle: $link-decoration,\r\n\t\t\t\t$hoverstyle: $link-decoration-over,\r\n\t\t\t\t$backgroundcolor: $backgroundcolor);\r\n\t\t}\r\n\t}\r\n\ta h1, a .h1, a .H1,\r\n\ta h2, a .h2, a .H2,\r\n\ta h3, a .h3, a .H3,\r\n\ta h4, a .h4, a .H4,\r\n\ta h5, a .h5, a .H5,\r\n\ta h6, a .h6, a .H6,\r\n\ta .headerfont {\r\n\t\t@include links(\r\n\t\t\t$normalcolor: $link-color,\r\n\t\t\t$hovercolor: $link-color-over,\r\n\t\t\t$normalstyle: $link-decoration,\r\n\t\t\t$hoverstyle: $link-decoration-over,\r\n\t\t\t$backgroundcolor: $backgroundcolor);\r\n\t}\r\n}\r\n\r\n\r\n// Example usage:\r\n//@include heading-styles(\r\n//\t$color: #333,\r\n//\t$link-color: #007bff,\r\n//\t$link-color-over: #0056b3,\r\n//\t$link-decoration: none !important,\r\n//\t$link-decoration-over: underline\r\n//);\r\n\r\n// alias\r\n@mixin a {\r\n\t@include links($linknormal, $linkhover, linkDecNo, linkDecUnderline);\r\n}\r\n\r\n\r\n", "//COMMENT STYLES\r\n#comments-title {\r\n\tpadding: 0.75em;\r\n\tmargin: 0;\r\n\tborder-top: 1px solid $light-gray;\r\n}\r\n\r\n\r\n.commentlist {\r\n\tmargin: 0;\r\n\tlist-style-type: none;\r\n}\r\n\r\n.comment {\r\n\tposition: relative;\r\n\tclear: both;\r\n\toverflow: hidden;\r\n\tpadding: 1.5em;\r\n\tborder-bottom: 1px solid $light-gray;\r\n\r\n\t.comment-author {\r\n\t\tpadding: 7px;\r\n\t\tborder: 0;\r\n\t}\r\n\r\n\t.vcard {\r\n\t\tmargin-left: 50px;\r\n\r\n\t\tcite.fn {\r\n\t\t\tfont-weight: 700;\r\n\t\t\tfont-style: normal;\r\n\t\t}\r\n\r\n\t\ttime {\r\n\t\t\tdisplay: block;\r\n\t\t\tfont-size: 0.9em;\r\n\t\t\tfont-style: italic;\r\n\r\n\t\t\ta {\r\n\t\t\t\tcolor: $meta-gray;\r\n\t\t\t\ttext-decoration: none;\r\n\r\n\t\t\t\t&:hover {\r\n\t\t\t\t\ttext-decoration: underline;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.photo {}\r\n\r\n\t\t.avatar {\r\n\t\t\tposition: absolute;\r\n\t\t\tleft: 16px;\r\n\t\t\tborder-radius: 50%;\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t&:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.children {\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\r\n\t&[class*=depth-] {\r\n\t\tmargin-top: 1.1em;\r\n\t}\r\n\r\n\t&.depth-1 {\r\n\t\tmargin-left: 0;\r\n\t\tmargin-top: 0;\r\n\t}\r\n\r\n\t&:not(.depth-1) {\r\n\t\tmargin-top: 0;\r\n\t\tmargin-left: 7px;\r\n\t\tpadding: 7px;\r\n\t}\r\n\r\n\t&.odd {\r\n\t\tbackground-color: $white;\r\n\t}\r\n\r\n\t&.even {\r\n\t\tbackground: $light-gray;\r\n\t}\r\n}\r\n\r\n.comment_content {\r\n\tp {\r\n\t\tmargin: 0.7335em 0 1.5em;\r\n\t\tfont-size: 1em;\r\n\t\tline-height: 1.5em;\r\n\t}\r\n}\r\n\r\n.comment-reply-link {\r\n\tfont-size: 0.9em;\r\n\tfloat: right;\r\n\r\n\t&:hover,\r\n\t&:focus {\r\n\t}\r\n\r\n}\r\n\r\n.comment-edit-link {\r\n\tfont-style: italic;\r\n\tmargin: 0 7px;\r\n\ttext-decoration: none;\r\n\tfont-size: 0.9em;\r\n}\r\n\r\n//COMMENT FORM STYLES\r\n.comment-respond {\r\n\tpadding: 1.5em;\r\n\tborder-top: 1px solid $light-gray;\r\n\r\n\t#reply-title {\r\n\t\tmargin: 0;\r\n\t}\r\n\r\n\t.logged-in-as {\r\n\t\tcolor: $meta-gray;\r\n\t\tfont-style: italic;\r\n\t\tmargin: 0;\r\n\r\n\t\ta {\r\n\t\t\tcolor: $text-color;\r\n\t\t}\r\n\t}\r\n\r\n\t.comment-form-comment {\r\n\t\tmargin: 1.5em 0 0.75em;\r\n\t}\r\n\r\n\t.form-allowed-tags {\r\n\t\tpadding: 1.5em;\r\n\t\tbackground-color: $light-gray;\r\n\t\tfont-size: 0.9em;\r\n\t}\r\n\r\n\t#submit {\r\n\t\tfloat: right;\r\n\t\tfont-size: 1em;\r\n\t}\r\n\r\n\t#comment-form-title {\r\n\t\tmargin: 0 0 1.1em;\r\n\t}\r\n\r\n\t#cancel-comment-reply {\r\n\t\ta {}\r\n\t}\r\n\r\n}\r\n\r\n\r\n.comments-logged-in-as {}\r\n\r\n#allowed_tags {\r\n\tmargin: 1.5em 10px 0.7335em 0;\r\n}\r\n\r\n.nocomments {\r\n\tmargin: 0 20px 1.1em;\r\n}\r\n", "// setting up defaults\r\ninput[type=\"text\"],\r\ninput[type=\"password\"],\r\ninput[type=\"datetime\"],\r\ninput[type=\"datetime-local\"],\r\ninput[type=\"date\"],\r\ninput[type=\"month\"],\r\ninput[type=\"time\"],\r\ninput[type=\"week\"],\r\ninput[type=\"number\"],\r\ninput[type=\"email\"],\r\ninput[type=\"url\"],\r\ninput[type=\"search\"],\r\ninput[type=\"tel\"],\r\ninput[type=\"color\"],\r\ninput[type=\"submit\"],\r\nbutton,\r\nselect,\r\ntextarea,\r\n.field {\r\n\tcolor: $text-color;\r\n\tbackground-color: $white;\r\n\r\n\tborder: 1px solid $grey;\r\n\tborder-radius: 3px;\r\n\tbox-shadow: none;\r\n\tdisplay: block;\r\n\tpadding: 0 0 0 12px;\r\n\tmargin-bottom: ($wrapper_padding / 4);\r\n\r\n\tfont-size: 1em;\r\n\tvertical-align: middle;\r\n\theight: 40px;\r\n\tline-height: 1;\r\n\twidth: 100%;\r\n\tmax-width: 400px;\r\n\tfont-family: $font_mono;\r\n\ttransition: background-color 0.24s ease-in-out;\r\n\r\n\t&:focus,\r\n\t&:active {\r\n\t\tbackground-color: #efefef;\r\n\t\toutline: none;\r\n\t\tborder: 1px solid #bbb !important;\r\n\t}\r\n\r\n\r\n\t&[disabled],\r\n\t&.is-disabled {\r\n\t\tcursor: not-allowed;\r\n\t\tborder-color: $grey;\r\n\t\topacity: 0.6;\r\n\r\n\t\t&:focus,\r\n\t\t&:active {\r\n\t\t\tbackground-color: $grey;\r\n\t\t}\r\n\t}\r\n}\r\n\r\nbutton, input[type=\"submit\"], label.fileButton, .button {\r\n\tfont-family: inherit; // this inherits from the main font.  Otherwise, it would be Courier, which looks kinda bad, on a button.\r\n\tfont-weight: bold;\r\n\r\n\theight: auto;\r\n\tpadding: 5px 13px 6px 12px; //neither % nor vw works too great, here.\r\n\r\n\tborder-color: $medgrey;\r\n\tborder: 1px solid;\r\n\tborder-radius: 3px;\r\n\r\n\tbackground: $white;\r\n\tmargin: 0;\r\n\tmax-width: fit-content;\r\n\tdisplay: inline-block;\r\n\r\n\tcursor: pointer;\r\n\r\n\r\n\t//blueberry powerpress. might have to take this button stuff outta here if this keeps ocuurrrring\r\n\t.powerpress_player & {\r\n\t\tmax-width: none;\r\n\t}\r\n}\r\n\r\n// this is for function fileInput($inputLabels) is used 2025-02-14\r\n.formFileInputs, .formFileInput {\r\n\tinput[type=\"file\"] {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n\r\ninput[type=\"checkbox\"], input[type=\"radio\"] {\r\n\tcursor: pointer;\r\n}\r\n\r\ninput[type=\"checkbox\"] + label, input[type=\"radio\"] + label {\r\n\tcursor: pointer;\r\n}\r\n\r\n\r\n.radio-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\t//<input type=\"radio\" {}\r\n\tlabel {\r\n\t\tmargin-left: 5px; /* Adjust the spacing between the radio button and its label */\r\n\t}\r\n}\r\n\r\n\r\ninput[type=\"password\"] {\r\n\tletter-spacing: 0.3em; // spacing out the passwordd\r\n}\r\n\r\ntextarea {\r\n\tmax-width: 100%;\r\n\tmin-height: 120px;\r\n\tline-height: 1.5em;\r\n}\r\n\r\n[contenteditable=\"true\"]:hover {\r\n\tcursor: pointer;\r\n}\r\n\r\n.error_msg {\r\n\tbackground: none repeat scroll 0 0 $form_error_bg;\r\n\tborder: 1px solid $form_red;\r\n\tcolor: $white;\r\n}\r\n\r\n// this is all jquery validate\r\nform {\r\n\t//<editor-fold desc=\"-- jQuery Validate -- \">\r\n\t.requiredwrap {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.requiredlabel:after {\r\n\t\tcontent: \"*\";\r\n\t\tcolor: $form_red;\r\n\t}\r\n\r\n\tlabel.requiredlabel.error {\r\n\t\tcolor: $form_red;\r\n\t\tright: 1px;\r\n\t\ttop: 0px;\r\n\t}\r\n\r\n\tlabel.error:after {\r\n\t\tcontent: \" --Required\";\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\tlabel.requiredlabel.invalid:after,\r\n\tlabel.requiredlabel.error.invalid:after {\r\n\t\tcontent: \" --Invalid\";\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\tinput.error,\r\n\tinput.error:focus, // probly this could be almost all types\r\n\tinput[type=\"text\"].error,\r\n\tinput[type=\"text\"].error:focus,\r\n\tselect.error,\r\n\ttextarea.error,\r\n\ttextarea.error:focus {\r\n\t\tbackground: none repeat scroll 0 0 $form_error_bg; //$alert-red\r\n\t\tborder: 1px solid $form_red;\r\n\t\tcolor: $white;\r\n\t}\r\n\r\n\tinput[type=\"text\"].error,\r\n\tinput[type=\"password\"].error,\r\n\tinput[type=\"datetime\"],\r\n\tinput[type=\"datetime-local\"].error,\r\n\tinput[type=\"date\"].error,\r\n\tinput[type=\"month\"].error,\r\n\tinput[type=\"time\"].error,\r\n\tinput[type=\"week\"].error,\r\n\tinput[type=\"number\"].error,\r\n\tinput[type=\"email\"].error,\r\n\tinput[type=\"url\"].error,\r\n\tinput[type=\"search\"].error,\r\n\tinput[type=\"tel\"].error,\r\n\tinput[type=\"color\"].error,\r\n\tbutton.error,\r\n\tselect.error,\r\n\ttextarea.error {\r\n\t\tmargin-bottom: 0 !important;\r\n\t}\r\n\r\n\tlabel.error {\r\n\t\tmargin-bottom: 20px;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t//</editor-fold> -- jQuery Validate --\r\n}\r\n\r\n\r\n", "\r\ntable.xdebug-error, table.xe-warning {\r\n\tfont-size: 14px !important;\r\n\tfont-weight: 400 !important;\r\n\tfont-family: \"Courier New\", Courier, monospace;\r\n\tcolor: #000 !important;\r\n\t\r\n\t&:hover {\r\n\t\t@include links(#000, #000 !important, linkDecUnderline, linkDecUnderline); // use important in quotes 'linkDecUnderline !important'\r\n\t\tcolor: #000 !important;\r\n\t}\r\n\t\r\n\t@include links(#000, #000 !important, linkDecUnderline, linkDecUnderline); // use important in quotes 'linkDecUnderline !important'\r\n\tth {\r\n\t\ttd {\r\n\t\t\tfont-weight: 400 !important;\r\n\t\t\t@include links(#000, #000 !important, linkDecUnderline, linkDecUnderline); // use important in quotes 'linkDecUnderline !important'\r\n\t\t\tcolor: #000 !important;\r\n\t\t\t\r\n\t\t\t&:hover {\r\n\t\t\t\t@include links(#000, #000 !important, linkDecUnderline, linkDecUnderline); // use important in quotes 'linkDecUnderline !important'\r\n\t\t\t\tcolor: #000 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n#atmedia_test {\r\n\tdisplay: none !important;\r\n}\r\n\r\nbody.is_dev .is_dev { //dugdebug\r\n\tdisplay: inherit !important; //idk what this is for.\r\n}\r\n\r\n//<editor-fold desc=\"-- debug with colored boxes etc  -- \">\r\n\r\n\r\n//body.is_dev {\r\n//\t//body{\r\n//\t.wrapper, .fl-row-fixed-width{\r\n//\t\tbox-shadow      : none;\r\n//\t\tbackground-clip : unset;\r\n//\r\n//\t\t// un comment, for the padding show thing.\r\n//\t\t//@media screen and (max-width : ($wrapperpx + $wrapperpxBuffer)) {\r\n//\t\t//\tbackground-clip : content-box;\r\n//\t\t//\tbox-shadow      : inset 0 0 0 1vw lightGreen; // just for testing.\r\n//\t\t//\t.fl-row-fixed-width{  // this is for inner rows, like  .fl-row-fixed-width .fl-row-fixed-width {}\r\n//\t\t//\t\tbox-shadow: none ;\r\n//\t\t//\t\tbackground-clip: unset;\r\n//\t\t//\t}\r\n//\t\t//}\r\n//\t}\r\n//\r\n//\t&.is_mobile{\r\n//\t\t#atmedia_test {\r\n//\t\t\tfont-size : 150%;\r\n//\t\t}\r\n//\t}\r\n//\t#atmedia_test{\r\n//\t\tdisplay : none;\r\n//\t}\r\n//#atmedia_test{\r\n//  display          : inline-block !important;\r\n//\tposition         : fixed;\r\n//\tz-index          : 99999999;\r\n//\tbottom              : 20px;\r\n//\tleft             : 22px;\r\n//\tborder           : 1px solid grey;\r\n//\tbackground-color : #fff;\r\n//\t#testbox_mob{\r\n//\t\tdisplay: inline-block;\r\n//\t}\r\n//\t.testbox{\r\n//\t\tdisplay : none;\r\n//\t}\r\n//\r\n//\t// these are the 'less than ipad' etc..\r\n//\r\n//\t@media screen and (max-width : $wrapperpx){\r\n//\t\t#minusWrapperPX{\r\n//\t\t\tdisplay          : inline-block;\r\n//\t\t\tbackground-color : lightblue;\r\n//\t\t}\r\n//\t}\r\n//\r\n//\t@media screen and (max-width : $atMedia_ipad_max-width){\r\n//\t\t#minusWrapperPX{\r\n//\t\t\tdisplay : none;\r\n//\t\t}\r\n//\t\t#minus768{\r\n//\t\t\tdisplay          : inline-block;\r\n//\t\t\tbackground-color : lightsalmon;\r\n//\t\t}\r\n//\t}\r\n//\r\n//\t@media screen and (max-width : $small_max-width){// this is useless.\r\n//\t\t#minus550{\r\n//\t\t\tdisplay          : inline-block;\r\n//\t\t\tbackground-color : lightyellow;\r\n//\t\t}\r\n//\t\t#minus768{\r\n//\t\t\tdisplay : none;\r\n//\t\t}\r\n//\t\t#minusWrapperPX{\r\n//\t\t\tdisplay : none;\r\n//\t\t}\r\n//\t}\r\n//}\r\n//}\r\n\r\n\r\n// this is the test boxes, in d_global / d_misc.php\r\n//<div id=\"atmedia_test\" style=\"display: none\">\r\n//\t<div class=\"testbox\" id=\"minus768\">less than 768</div>\r\n//\t<div class=\"testbox\" id=\"minusWrapperPX\">less than WrapperPX</div>\r\n//</div>\r\n//</editor-fold> -- debug with colored boxes etc --\r\n\r\n", "\r\ntable {\r\n\twidth: 100%;\r\n\tmargin-bottom: 1.5vw;\r\n\t\r\n\ttr {\r\n\t\tborder-bottom: 1px solid $barelygrey;\r\n\t\t\r\n\t\tth {\r\n\t\t\tbackground-color: $barelygrey;\r\n\t\t}\r\n\t\t\r\n\t\ttd {\r\n\t\t\tpadding: 7px;\r\n\t\t}\r\n\t\t\r\n\t}\r\n\t\r\n\tcaption {\r\n\t\tmargin: 0 0 7px;\r\n\t\tfont-size: 0.75em;\r\n\t\tcolor: $meta-gray;\r\n\t\ttext-transform: uppercase;\r\n\t\tletter-spacing: 1px;\r\n\t}\r\n\t\r\n}\r\n\r\n/* xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx   */\r\n.tinytable td, .tinytable th {\r\n\tpadding: 0px 10px 0px 0 !important;\r\n\tfont-size: 10px;\r\n\ttext-align: right;\r\n}\r\n\r\n.tinytable td:last-child, .tinytable th:last-child {\r\n\tpadding-right: 0 !important;\r\n}\r\n\r\n.tinytable > tbody > tr:last-child td {\r\n\tfont-weight: bold;\r\n\tborder-top: 1px solid #999;\r\n}\r\n", "// use content to get past the defaults.  wierd specificity, but ok.\r\ntable.zebra > tbody,\r\n#content table.zebra > tbody,\r\n.content table.zebra > tbody {\r\n\twidth: 100%;\r\n\r\n\t& > tr:nth-child(odd) {\r\n\t\tbackground-color: $table_zebra_tr_odd_bg;\r\n\t}\r\n\r\n\t& > tr:nth-child(even) {\r\n\t\tbackground-color: $table_zebra_tr_even_bg;\r\n\t}\r\n\r\n\t& > tr > td,\r\n\t& > tr > th {\r\n\t\tpadding: 2px 10px;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t& > tr > th {\r\n\t\tbackground-color: $table_zebra_th_bg;\r\n\t\tfont-size: 14px;\r\n\t\ttext-transform: uppercase;\r\n\t\ttext-align: center !important;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: $table_zebra_th_color; //#000;\r\n\t}\r\n\r\n\r\n\t//fuck is this?\r\n\t//& > tr > th,\r\n\t//& > tr:nth-last-child(2) td {\r\n\t//\tmax-width: 2px;\r\n\t//}\r\n\r\n}\r\n\r\ntable.zebra.borders > tbody,\r\n#content table.zebra.borders > tbody,\r\n.content table.zebra.borders > tbody {\r\n\r\n\t& > tr {\r\n\t\t&:first-child {\r\n\t\t\tborder-bottom: $dkgrey solid 1px; // the header\r\n\t\t}\r\n\r\n\t\t&last-child {\r\n\t\t\tborder-bottom: $medgrey solid 1px; // the bottom\r\n\t\t}\r\n\t}\r\n\r\n\t& > tr > th {\r\n\t\tborder-right: $table_zebra_th_borderright;\r\n\r\n\t\t&:last-child {\r\n\t\t\tborder-right: none;\r\n\t\t}\r\n\t}\r\n\r\n\t& > tr > td {\r\n\t\tborder-right: 1px dashed $ltgrey;\r\n\r\n\t\t&:last-child {\r\n\t\t\tborder-right: none;\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n\r\ntable.zebra.debug > tbody,\r\n#content table.zebra.debug > tbody,\r\n.content table.zebra.debug > tbody {\r\n\ttd {\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n}", "$font_normal: $sans;\r\n\r\n$fontcolor: $black;\r\n$linkcolor: $black;\r\n$linkover: $dkpurple;\r\n\r\n\r\n$font_header_weight: 900;\r\n$font_header: \"Poppins\", 'Arial', 'Helvetica', 'Liberation Sans', Tahoma, Geneva, sans-serif;\r\n$font_nav: $font_header;\r\n\r\n\r\n$headerBackground: $dkpurple;\r\n$footerBackground: $dkpurple;\r\n\r\n\r\n$H1_color: $dkblue;\r\n$H1_link: $dkblue;\r\n$H1_link_over: $dkpurple;\r\n\r\n\r\n$fontColorDkBg: $white;\r\n$linkcolorDkBg: $white;\r\n$linkoverDkBg: $ltgrey;\r\n$H1_colorDkBg: $white;\r\n$H1_linkDkBg: $white;\r\n$H1_link_overDkBg: $ltgrey;\r\n\r\n\r\n$wprmenu_menubar_background: $dkpurple; //$white;//$dkpurple\r\n$wprmenu_menubar_burger_color: $white;\r\n$wprmenu_menu_ul: $white;\r\n$wprmenu_menu_li_active: $dkwhite;\r\n$wprmenu_color: $fontcolor;\r\n$wprmenu_color_over: $fontcolor;\r\n\r\n\r\n//zebra -----------------------------------------------------------\r\n\r\n$table_zebra_tr_even_bg: $white;\r\n$table_zebra_tr_odd_bg: rgba($ltblue, .1);\r\n\r\n$table_zebra_th_bg: $ltblue;\r\n$table_zebra_th_color: $white;\r\n$table_zebra_th_borderright: transparent;\r\n\r\n\r\n\r\n", "//\r\n//\r\n.cardscontainer {\r\n\t@include container;\r\n\r\n\t.cardswrapper {\r\n\t\t@include wrapper;\r\n\r\n\t\t.cardsgrid {\r\n\t\t\t//\t@include gridAutoColumns;\r\n\t\t\t//@include gridColumns4432;\r\n\t\t\t@include gridColumns3321;\r\n\r\n\t\t}\r\n\t}\r\n}\r\n\r\n//<div class=\"card\">\r\n//\t<div class=\"wrap\">\r\n//\r\n//\t\t<div class=\"imgwrap\">\r\n//\t\t\t<img src=\"asdfasdf.png\"/>\r\n//\t\t</div>\r\n//\r\n//\t\t<div class=\"textwrap\">\r\n//\r\n//\t\t\t<div class=\"header\">\r\n//\t\t\t\t<div class=\"headerwrap\">\r\n//\t\t\t\t\t<h2>Blarg Blorg</h2>\r\n//\t\t\t\t\t<div class=\"subtitle\">Warbl Dee Bluup</div>\r\n//\t\t\t\t</div>\r\n//\t\t\t</div>\r\n//\r\n//\t\t\t<div class=\"blurb\">\r\n//\t\t\t\t<div class=\"blurbwrap\">\r\n//\t\t\t\t\tasdfasdf asdfasdfa adlfkjlk jdslie iejls kjsadle.\r\n//\t\t\t\t</div>\r\n//\t\t\t</div>\r\n//\r\n//\t\t\t<div class=\"footer\">\r\n//\t\t\t\t<div class=\"footerwrap\">\r\n//\t\t\t\t\t<a href=\"/somelink/\">Click Here</a>\r\n//\t\t\t\t</div>\r\n//\t\t\t</div>\r\n//\t\t</div>\r\n//\t</div>\r\n//</div>\r\n\r\n.card {\r\n\t@include shadowtwo();\r\n\t@include responsive-border-radius;\r\n\toverflow: hidden;\r\n\r\n\t// Enable container queries\r\n\tcontainer-type: inline-size;\r\n\tcontainer-name: card;\r\n\r\n\ta { // since the whole card is a link, we need to reset the link styles\r\n\t\t@include links($fontcolor, $fontcolor, linkDecNo, linkDecNo);\r\n\t\t@include headers($color: $H1_color,\r\n\t\t$link-color: $H1_link,\r\n\t\t$link-color-over: $H1_link_over,\r\n\t\t$link-decoration: none,\r\n\t\t$link-decoration-over: none,\r\n\t\t$backgroundcolor: #fff);\r\n\t}\r\n\r\n\t.wrap {\r\n\t\t.imgwrap {\r\n\t\t\twidth: 100%;\r\n\t\t\tmargin: 0 auto 1vw;\r\n\r\n\t\t\timg {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.textwrap {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t.header, .blurb, .footer {\r\n\t\t\t\tmargin-bottom: 2vw;\r\n\t\t\t}\r\n\r\n\t\t\t.headerwrap, .blurbwrap, .footerwrap {\r\n\t\t\t\t@include responsive-padding-01_5-02;\r\n\t\t\t}\r\n\r\n\t\t\t.header {\r\n\t\t\t\t.headerwrap {\r\n\t\t\t\t\th2 {\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tmargin: 0 auto 1vw;\r\n\t\t\t\t\t\t// kinda sucky overflow-wrap: break-word;\r\n\t\t\t\t\t\thyphens: auto;\r\n\t\t\t\t\t\t// @include fluid-container-type(18px, 26px, 225px, $small_max-width); // Min size 18px, max 26px\r\n\t\t\t\t\t\t// @include sass-dump($h2-size);\r\n\t\t\t\t\t\tfont-size: clamp(calc($h2-size * .70), 8cqi, $h2-size);\r\n\t\t\t\t\t\t/* Min: 1 rem, Preferred: 4% of container, Max:1.75rem */\r\n\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t\t.subtitle {\r\n\t\t\t\t\t\ttext-transform: capitalize;\r\n\t\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t\tfont-style: italic;\r\n\t\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\t\t// kinda sucky overflow-wrap: break-word;\r\n\t\t\t\t\t\thyphens: auto;\r\n\t\t\t\t\t\t@include fluid-container-type(14px, 18px, 225px, $small_max-width); // Min size 14px, max 18px\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.blurb {\r\n\t\t\t\t.blurbwrap {\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.footer {\r\n\t\t\t\t.footerwrap {\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\toverflow-wrap: break-word;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// -----------------------------------------------------------------------------------------------------------------------\r\n\r\n//<editor-fold desc=\"-- headerblurbcard -- \">\r\n\r\n//<div class=\"headerblurbcard\">\r\n//\t<div class=\"wrap\">\r\n//\r\n//\t\t<div class=\"header\">\r\n// \t\t\t<div class=\"headerwrap\">\r\n//\t\t\t\t<h2>Blarg Blorg</h2>\r\n//\t\t\t</div>\r\n//\t\t</div>\r\n//\r\n//\t \t<div class=\"blurb\">\r\n//\t\t\t<div class=\"blurbwrap\">\r\n//\t\t\t\tasdfasdf asdfasdfa adlfkjlk jdslie iejls kjsadle.\r\n//\t\t\t</div>\r\n//\t\t</div>\r\n//\r\n//\t\t<div class=\"footer\">\r\n//\t\t\t<div class=\"footerwrap\">\r\n//\t\t\t\t<a href=\"/somelink/\">Click Here</a>\r\n//\t\t\t</div>\r\n//\t\t</div>\r\n//\t</div>\r\n//</div>\r\n// unsed but this is like from salmonforsoldiers. (which was headerblubpanel)\r\n// not tryed or used. 2025-05-30\r\n.headerblurbcard {\r\n\t.wrap {\r\n\t\twidth: 100%;\r\n\t\tpadding: 2vw;\r\n\r\n\t\t.header .headerwrap {\r\n\t\t\th2 {\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.blurb .blurbwrap {\r\n\t\t\ttext-align: left;\r\n\t\t}\r\n\r\n\t\t.footer .footerwrap {\r\n\t\t\ttext-align: center;\r\n\t\t\tmargin: 2vw auto;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n//</editor-fold> -- headerblurbcard --\r\n\r\n", "// --------------------------------------------------------\r\n$bodyBGcolor: $white;\r\n$fontcolor: $dkgrey;\r\n$linkcolor: $dkblue;\r\n$linkover: $ltblue;\r\n\r\n$fontColorDkBg: $dkwhite;\r\n$linkcolorDkBg: $dkwhite;\r\n$linkoverDkBg: $white;\r\n\r\n$fontColor_header: $fontColorDkBg;\r\n$fontColor_link_header: $fontColorDkBg;\r\n$fontColor_link_header_over: $linkoverDkBg;\r\n\r\n$fontColor_footer: $fontColor_header;\r\n$fontColor_link_footer: $fontColor_link_header;\r\n$fontColor_link_footer_over: $fontColor_link_header_over;\r\n\r\n$H1_color: $fontcolor;\r\n$H1_link: $fontcolor;\r\n$H1_link_over: $ltblue;\r\n$H1_shadow: 1.5px 1.5px 0 $medblue;\r\n\r\n$H1_colorDkBg: $dkwhite;\r\n$H1_linkDkBg: $dkwhite;\r\n$H1_link_overDkBg: $ltgrey;\r\n\r\n$h1-size: $h1_ems * $H1_multiplier !default;\r\n$h2-size: $h2_ems * $H1_multiplier !default;\r\n$h3-size: $h3_ems * $H1_multiplier !default;\r\n$h4-size: $h4_ems * $H1_multiplier !default;\r\n$h5-size: $h5_ems * $H1_multiplier !default;\r\n$h6-size: $h6_ems * $H1_multiplier !default;\r\n\r\n$H1_topAlign: $H1_topAlign * $H1_multiplier !default;\r\n$H1_margin-bottom: $H1_margin-bottom * $H1_multiplier !default;\r\n$H1_line-height: $H1_line-height * $H1_multiplier !default;\r\n\r\n// THING colors ---------------------------------------------------\r\n// gallery\r\n$d_gallery_title_bg: $white;\r\n$d_gallery_gslide-title: $H1_color;\r\n$d_gallery_gslide-desc: $dkgrey;\r\n$d_gallery_masonry_gutter: 1.5%;\r\n\r\n$footerBackground: $black;\r\n$headerBackground: $black;\r\n\r\n$navcolor: $fontColorDkBg;\r\n$navcolor_over: $linkoverDkBg;\r\n\r\n$wprmenu_menubar_background: $black;\r\n$wprmenu_menubar_burger_color: $dkwhite;\r\n$wprmenu_menu_ul: $black;\r\n$wprmenu_menu_li_active: $dkblue;\r\n$main_menu_border: none;\r\n$wprmenu_color: $dkwhite;\r\n$wprmenu_color_over: $dkwhite;\r\n\r\n$table_zebra_tr_odd_bg: $ltgrey;\r\n$table_zebra_tr_even_bg: $white;\r\n$table_zebra_th_bg: $medgrey;\r\n$table_zebra_th_color: $black;\r\n$table_zebra_th_borderright: 1px dashed $moregrey; //transparent;\r\n\r\n\r\n// fonts ----------------------------------------------------------------------------------------\r\n// i dont really know why... seems superflous, but maybe i had a reason.\r\n$font_normal: $sans;\r\n$font_serif: $serif;\r\n$font_mono: $mono;\r\n$font_narrow: $narrow;\r\n\r\n\r\n$font_header: $serif;\r\n$font_nav: $font_header;\r\n\r\n// dimensions --------------------------------------------------------------------\r\n$nav_wpr_height: 60px;\r\n$nav_wpr_space: 100px; //this is for adding space, a margin, under the wprmenubar\r\n\r\n//$wrapperpx + $wrapperpxBuffer → 1100px + 50 →  becomes 1150px\r\n//1150px + 1 → becomes 1151px\r\n//Since the first value has a unit, Sass is OK with adding unitless numbers to it.\r\n$wrapperpx: 1100px;\r\n$wrapperpxBuffer: 50; //yes, should be just a number.  will be added to whatever.   this just gives a little extra so that if the wrapper is right at the $wrapperpx, it's not touching\r\n\r\n$wrapper_padding: 4vw;\r\n\r\n$atMedia_ipad_max-width: 768px; // up_to_ipad {\t\t\t\t\t\t\t\t    @media screen and (max-width: $atMedia_ipad_max-width)\r\n$atMedia_ipad_min-width: ($atMedia_ipad_max-width + 1); // more_than_ipad {\t@media screen and (min-width: $atMedia_ipad_min-width)\r\n\r\n$small_max-width: 550px; //ok if you do this (not in quotes) , you can add +5 to it. orwhatever\r\n$small_min-width: ($small_max-width + 1);\r\n\r\n$wrapper_max-width: ($wrapperpx + $wrapperpxBuffer);\r\n$wrapper_min-width: ($wrapper_max-width +1);\r\n\r\n// mixins for these on the _beaverbuilder_global.scss\r\n$beaverbuilder_LargeMaxWidth: 1200px; //up_to_large {@media screen and (max-width: $beaverbuilder_LargeMaxWidth)\r\n$beaverbuilder_LargeMinWidth: ($beaverbuilder_LargeMaxWidth + 1); //more_than_large {@media screen and (min-width: $beaverbuilder_LargeMinWidth)\r\n\r\n$beaverbuilder_MediumMaxWidth: 992px;\r\n$beaverbuilder_MediumMinWidth: ($beaverbuilder_MediumMaxWidth + 1);\r\n\r\n$beaverbuilder_SmallMaxWidth: 768px;\r\n$beaverbuilder_SmallMinWidth: ($beaverbuilder_SmallMaxWidth + 1);\r\n\r\n\r\n// overlays and SHADOWS -------------------------------------------------------------\r\n$header_shadow: 1.5px 1.5px 0 $medblue;\r\n$textshadow: .5px .5px .5px rgba(0, 0, 0, 0.5);\r\n$transparentWhite01: rgba(255, 255, 255, 0.01);\r\n$transparentWhite05: rgba(255, 255, 255, 0.05);\r\n$transparentWhite6: rgba(255, 255, 255, 0.6);\r\n$transparentWhite8: rgba(255, 255, 255, 0.8);\r\n$overlay: rgba(16, 18, 19, 0.7);\r\n\r\n\r\n//bak compat ------------------------------------------------------\r\n$border-color: $grey;\r\n$disabled-gray: $ltgrey;\r\n$light-gray: $ltgrey;\r\n$gray: $grey;\r\n$text-color: $dkgrey;\r\n$meta-gray: $dkgrey;\r\n$bones-pink: $pink;\r\n$UnderWrapperSidePadding: $wrapper_padding; // i think this is crap\r\n\r\n", "@mixin gridGap234($gap: $wrapper_padding) {\r\n\tgrid-gap: ($gap / 2);\r\n\r\n\t@include up_to_ipad {\r\n\t\tgrid-gap: ($gap / 1.333);\r\n\t}\r\n\t@include up_to_small {\r\n\t\tgrid-gap: $gap;\r\n\t}\r\n}\r\n@mixin gridColumns5432 {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(5, 1fr);\r\n\t@include gridGap234;\r\n\t@include up_to_wrapper {\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t}\r\n\t@include up_to_ipad {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\t@include up_to_small {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t}\r\n}\r\n@mixin gridColumns4432 {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(4, 1fr);\r\n\t@include gridGap234;\r\n\t@include up_to_wrapper {\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t}\r\n\t@include up_to_ipad {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\t@include up_to_small {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t}\r\n}\r\n@mixin gridColumns4431 {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(4, 1fr);\r\n\t@include gridGap234;\r\n\t@include up_to_wrapper {\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t}\r\n\t@include up_to_ipad {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\t@include up_to_small {\r\n\t\tgrid-template-columns: repeat(1, 1fr);\r\n\t}\r\n}\r\n@mixin gridColumns3332 {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(3, 1fr);\r\n\t@include gridGap234;\r\n\t@include up_to_wrapper {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\t@include up_to_ipad {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t}\r\n\t@include up_to_small {\r\n\t\tgrid-template-columns: repeat(1, 1fr);\r\n\t}\r\n}\r\n@mixin gridColumns3322 {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(3, 1fr);\r\n\t@include gridGap234;\r\n\t@include up_to_wrapper {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\t@include up_to_ipad {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t}\r\n\t@include up_to_small {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t}\r\n}\r\n@mixin gridColumns3321 {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(3, 1fr);\r\n\t@include gridGap234;\r\n\t@include up_to_wrapper {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\t@include up_to_ipad {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t}\r\n\t@include up_to_small {\r\n\t\tgrid-template-columns: repeat(1, 1fr);\r\n\t}\r\n\r\n}\r\n//  your starting number of columns is based on the min-width of the columns.\r\n//  250px is good for 3 columns in 1100px container.\r\n@mixin gridAutoColumns($min-width: 250px, $grid-gap: $wrapper_padding) {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(auto-fit, minmax($min-width, 1fr));\r\n\tgrid-auto-rows: minmax(auto, max-content); // Makes rows adjust to content height\r\n\t@include gridGap234($grid-gap);\r\n}\r\n\r\n\r\n\r\n", "\r\n.wp-caption p.wp-caption-text {\r\n\tcolor: $dkblue;\r\n}\r\n\r\n.entry-meta {\r\n\tfont-size: 9px;\r\n\tmargin: -1% 0 3% 0;\r\n}\r\n\r\nhr.separator-break {\r\n\tdisplay: block;\r\n\tmargin: 40px auto !important;\r\n\ttext-align: center !important;\r\n}\r\n\r\ndiv#comments {\r\n\tdisplay: none !important; /* hide for now  */\r\n}\r\n\r\n//see svgSpinner3()\r\ndiv#spinnerholder {\r\n\tbackground: rgba(0, 0, 0, 0.75);\r\n\tdisplay: none;\r\n\theight: 100vh;\r\n\t// idk why this... margin-top: -14px;\r\n\ttext-align: center;\r\n\twidth: 100vw;\r\n\tz-index: 1111;\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\r\n\tsvg.spinner-svg {\r\n\t\tposition: absolute;\r\n\t\ttop: 50%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translate(-50%, -50%);\r\n\t}\r\n}\r\n\r\nsvg.spinner-svg {\r\n\twidth: 200px;\r\n\theight: 200px;\r\n\r\n\t.gradient-stop {\r\n\t\tstop-color: #fff;\r\n\t\tstop-fill: #fff;\r\n\t\tfill: #fff !important; /* Make sure to set your color here */\r\n\t}\r\n}\r\n\r\n\r\n", "// menu bar header\r\n#wprmenu_bar.d_mod * {\r\n\ttext-rendering: optimizeLegibility;\r\n}\r\n\r\n#wprmenu_bar.d_mod {\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tmargin: 0 !important;\r\n\tpadding: 2px ($wrapper_padding / 2) !important;\r\n\tmax-width: 100% !important;\r\n\theight: $nav_wpr_height;\r\n\tbackground: $wprmenu_menubar_background;\r\n\tcolor: $wprmenu_color;\r\n\tfont-size: 17px;\r\n\r\n\ta {\r\n\t\t@include links($wprmenu_color, $wprmenu_color_over !important, linkDecNo, linkDecUnderline); // use important in quotes 'linkDecUnderline !important'\r\n\t}\r\n\r\n\t//d_wprm_menu_title\r\n\t//d_wprm_bar_logo\r\n\t//d_wprm_hamburger\r\n\r\n\t#d_wprm_bar_logo {\r\n\t\tborder: none;\r\n\t\tmargin: 0 ($wrapper_padding / 2) 0 0;\r\n\t\tpadding: 0;\r\n\r\n\t\timg.bar_logo {\r\n\t\t\tmax-height: ($nav_wpr_height - 4px); //yes you need this\r\n\t\t\theight: auto;\r\n\t\t}\r\n\t}\r\n\r\n\t#d_wprm_menu_title {\r\n\t\twidth: auto;\r\n\t\tpadding: 10px 11px 11px 0;\r\n\t\ttext-align: left;\r\n\t\tdisplay: block;\r\n\t\tfont-family: $font_nav;\r\n\t\ttext-transform: uppercase;\r\n\t\tfont-size: 26px;\r\n\t\tcolor: $fontcolor;\r\n\t\t@media screen and (max-width: 400px) {\r\n\t\t\tfont-size: 18px;\r\n\t\t}\r\n\t\t@media screen and (max-width: 300px) {\r\n\t\t\tfont-size: 14px;\r\n\t\t}\r\n\t}\r\n\r\n\t#d_wprm_hamburger.hamburger {\r\n\t\ttext-align: center;\r\n\t\tpadding-right: 0 !important;\r\n\t\tmargin-top: 0;\r\n\r\n\t\t.hamburger-box {\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t#menuburgertext {\r\n\t\t\tfont-size: 13px;\r\n\t\t\ttext-transform: lowercase;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: $wprmenu_menubar_burger_color;\r\n\t\t\tfont-family: $font_nav;\r\n\t\t}\r\n\r\n\t\t.hamburger-inner,\r\n\t\t.hamburger-inner::before,\r\n\t\t.hamburger-inner::after {\r\n\t\t\tbackground: $wprmenu_menubar_burger_color;\r\n\t\t\theight: 5px;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\t.hamburger-inner {\r\n\t\t\ttop: 2px;\r\n\t\t}\r\n\r\n\t\t.hamburger-inner::before {\r\n\t\t\ttop: 10px;\r\n\t\t}\r\n\r\n\t\t.hamburger-inner::after {\r\n\t\t\ttop: 20px;\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t&, & * { // yes need this\r\n\t\tline-height: 1 !important;\r\n\t}\r\n\r\n}\r\n\r\n// the actual menu UL:\r\n.wprm-wrapper * {\r\n\ttext-rendering: optimizeLegibility;\r\n}\r\n\r\n.wprm-wrapper #mg-wprm-wrap { // this is theirs, but we do show it. its the menue\r\n\twidth: 100% !important;\r\n\tmax-width: none !important;\r\n\tbackground: $wprmenu_menu_ul;\r\n\ttop: ($nav_wpr_height) !important; //2 of these if overheader plus wprm header\r\n\r\n\t//$wprmenu_menu_ul: $verydarkgreengrey;\r\n\t//$wprmenu_menu_li_active: $dkblue;\r\n\r\n\r\n\t#wprmenu_menu_ul {\r\n\t\tbackground-color: $wprmenu_menu_ul;\r\n\t\tpadding: 0;\r\n\r\n\t\t// this is the open/close for subs\r\n\t\t.wprmenu_icon {\r\n\t\t\ttext-align: right;\r\n\t\t}\r\n\r\n\r\n\t\tli.menu-item {\r\n\t\t\tcolor: $wprmenu_color;\r\n\t\t\tborder-bottom: 1px solid rgba(68, 68, 68, .4);\r\n\r\n\t\t\ta {\r\n\t\t\t\t@include links($wprmenu_color !important, $wprmenu_color_over !important, linkDecNo, linkDecNo);\r\n\t\t\t\tbackground-color: transparent; //we need these\r\n\t\t\t\tfont-family: $font_nav;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tfont-size: 20px;\r\n\t\t\t\ttext-transform: capitalize;\r\n\t\t\t\tpadding: 2% 0 3% 2%;\r\n\t\t\t}\r\n\r\n\t\t\ta:hover {\r\n\t\t\t\tbackground-color: transparent; //we need these\r\n\t\t\t}\r\n\r\n\t\t\t&.current-menu-item {\r\n\t\t\t\tbackground-color: $wprmenu_menu_li_active;\r\n\r\n\t\t\t\t& > a {\r\n\t\t\t\t\tbackground-color: transparent; //we need these\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n//\r\n//\r\n//\r\n\r\n// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //\r\n// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //\r\n// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //\r\n// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //\r\n// boring // // boring // // boring // // boring // // boring // // boring // // boring // // boring //\r\n\r\n\r\nbody #body_container {\r\n\t@media screen and (max-width: $atMedia_ipad_max-width) {\r\n\t\t& {\r\n\t\t\tpadding-top: $nav_wpr_space;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// we dont want the extra space on front page.  this used to be on .fullwidth, too\r\nbody.is_front_page #body_container {\r\n\t@media screen and (max-width: $atMedia_ipad_max-width) {\r\n\t\t& {\r\n\t\t\tpadding-top: $nav_wpr_height;\r\n\t\t}\r\n\t}\r\n}\r\n\r\nbody #body_container {\r\n\t@media screen and (max-width: ($atMedia_ipad_max-width)) {\r\n\t\tmargin-top: 0 !important; // because this usually has margin, we need to kill it, when we're in wprm mode.  then we need to do padding different.\r\n\t}\r\n}\r\n\r\n\r\n// hider and shower\r\n@media screen and (min-width: $atMedia_ipad_max-width) {\r\n\t#headers_container {\r\n\t\tdisplay: block;\r\n\t}\r\n\t#wprmenu_bar.d_mod {\r\n\t\tdisplay: none;\r\n\t}\r\n}\r\n\r\n@media screen and (max-width: $atMedia_ipad_max-width) {\r\n\t#headers_container {\r\n\t\tdisplay: none;\r\n\t}\r\n\t#wprmenu_bar.d_mod {\r\n\t\tdisplay: flex !important;\r\n\t\tposition: absolute !important; //has to be this, esp if there is an overheader\r\n\t}\r\n}\r\n\r\n.wprm-wrapper > #wprmenu_bar.wprmenu_bar,\r\n.wprm-wrapper > #wprmenu_bar.wprmenu_bar div.hamburger { //this is theirs. ours is overlayed.\r\n\tdisplay: none;\r\n}\r\n\r\n\r\n\r\n", "//<editor-fold desc=\"-- header structure -- \">\r\n//<body >\r\n//<div id=\"headers_container\">\r\n//\t<div id=\"mainheader_container\"\r\n//\t\t<div id=\"mainheader_wrapper\"\r\n//\t\t\t<div id=\"logo-and-text\">\r\n//\t\t\t\t<div id=\"text\">\r\n//\t\t\t\t\t<div id=\"site-title-text\"><a href=\"/\"></div>\r\n//\t\t\t\t\t<div id=\"site-description\"></div>\r\n//\t\t\t\t</div>\r\n//\t\t\t</div>\r\n//\t\t\t<div id=\"menu\">\r\n//\t\t\t\t<nav id=\"main-menu\"></nav>\r\n//\t\t\t</div>\r\n//\t\t</div>\r\n//\t</div>\r\n//</div>\r\n//</editor-fold> -- header structure --\r\n\r\nnav#main-menu {\r\n\tul#menu-main {\r\n\r\n\t\ta {\r\n\t\t\tfont-family: $font_nav;\r\n\t\t\t//$navcolor: $fontColorDkBg;\r\n\t\t\t//$navcolor_over\r\n\t\t\tcolor: $navcolor !important;\r\n\t\t\t@include links($navcolor !important, $navcolor_over !important, linkDecNo, linkDecNo);\r\n\t\t\ttext-transform: $nav_text_transform;\r\n\t\t\ttext-decoration: none; \r\n\t\t\tfont-size: $font_nav_size;\r\n\t\t\tpadding: 10px;\r\n\t\t\tdisplay: block;\r\n\t\t\tposition: relative;\r\n\r\n\t\t\t&:focus, &:visited {\r\n\t\t\t\ttext-decoration: none; // weird as fuck, this dont work, cuz browsers   https://stackoverflow.com/questions/10471929/underlining-visited-links\r\n\t\t\t\ttext-decoration-color: $headerBackground !important; // this is what you gotta do to hide it.\r\n\t\t\t}\r\n\r\n\t\t\t&:hover {\r\n\t\t\t\ttext-decoration-color: $navcolor_over;\r\n\t\t\t\ttext-decoration: underline !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t//li.current-menu-item {\r\n\t\t//\ttext-decoration: underline $dkwhite !important;\r\n\t\t//}\r\n\r\n\t\t& > li {\r\n\t\t\tfloat: left; // this makes all the items line up horzontally\r\n\t\t\t&:last-child a {\r\n\t\t\t\tpadding-right: 0;\r\n\t\t\t}\r\n\r\n\t\t\t//<editor-fold desc=\"-- this is for a bigger, farther underline. sometimes looks cool -- \">\r\n\t\t\t//&:hover, &:focus, &.current-menu-item {\r\n\t\t\t//\tborder-bottom: $main_menu_border;\r\n\t\t\t//}\r\n\t\t\t//&.menu-item-has-children {\r\n\t\t\t//\t&:hover, &:focus {\r\n\t\t\t//\t\tborder-bottom: 0 !important;\r\n\t\t\t//\t}\r\n\t\t\t//}\r\n\t\t\t//</editor-fold> -- this is for a bigger, farther underline. sometimes looks cool --\r\n\r\n\t\t\t&.menu-item-has-children ul.sub-menu {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\r\n\t\t\t&.menu-item-has-children:hover ul.sub-menu {\r\n\t\t\t\tborder-bottom: 0 !important;\r\n\t\t\t\tbackground: $headerBackground;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tmargin-top: 0;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tz-index: 8999;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n\r\n\r\n", "//<editor-fold desc=\"-- header structure -- \">\r\n//<body >\r\n//<div id=\"headers_container\">\r\n//\t<div id=\"mainheader_container\"\r\n//\t\t<div id=\"mainheader_wrapper\"\r\n//\t\t\t<div id=\"logo-and-text\">\r\n//\t\t\t\t<div id=\"text\">\r\n//\t\t\t\t\t<div id=\"site-title-text\"><a href=\"/\"></div>\r\n//\t\t\t\t\t<div id=\"site-description\"></div>\r\n//\t\t\t\t</div>\r\n//\t\t\t</div>\r\n//\t\t\t<div id=\"menu\">\r\n//\t\t\t\t<nav id=\"main_menu_flex\">\r\n//\t\t\t\t\t<div class=\"menu-main-container\">\r\n//\t\t\t\t\t\t<ul id=\"menu-main\" class=\"nav top-nav cf\">\r\n//\t\t\t\t\t\t\t<li id=\"menu-item-2226\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-1845 current_page_item menu-item-has-children menu-item-2226\"><a href=\"/\" aria-current=\"page\">home</a>\r\n//\t\t\t\t\t\t\t\t<ul class=\"sub-menu\">\r\n//\t\t\t\t\t\t\t\t\t<li id=\"menu-item-2111\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-2111\"><a href=\"/about/\">About</a></li>\r\n//\t\t\t\t\t\t\t\t\t<li id=\"menu-item-2227\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-2227\"><a href=\"/chapters/\">Chapters</a></li>\r\n//\t\t\t\t\t\t\t\t</ul>\r\n//\t\t\t\t\t\t\t</li>\r\n//\t\t\t\t\t\t\t<li id=\"menu-item-2106\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-2106\"><a href=\"/partners/\">Partners</a></li>\r\n//\t\t\t\t\t\t\t<li id=\"menu-item-2228\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-2228\"><a href=\"/media/\">Media</a></li>\r\n//\t\t\t\t\t\t\t<li id=\"menu-item-2108\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-2108\"><a href=\"/faq/\">FAQ</a></li>\r\n//\t\t\t\t\t\t\t<li id=\"menu-item-2105\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-2105\"><a href=\"/contact/\">Contact</a></li>\r\n//\t\t\t\t\t\t\t<li id=\"menu-item-2232\" class=\"menu-item menu-item-type-post_type menu-item-object-page menu-item-2232\"><a href=\"/donate/\">Donate</a></li>\r\n//\t\t\t\t\t\t</ul>\r\n//\t\t\t\t\t</div>\r\n//\t\t\t\t</nav>\r\n//\t\t\t</div>\r\n//\t\t</div>\r\n//\t</div>\r\n//</div>\r\n//</editor-fold> -- header structure --\r\n\r\nnav#main_menu_flex {\r\n\t.menu-main-container {\r\n\t\twidth: 100%;\r\n\r\n\t\t// Top level menu\r\n\t\tul#menu-main {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tlist-style: none;\r\n\t\t\tmargin: 0;\r\n\t\t\tpadding: 0;\r\n\r\n\t\t\tli.menu-item {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tpadding: 0;\r\n\r\n\t\t\t\tz-index: 9;\r\n\r\n\t\t\t\t// for when the width gets small and the menu starts to wrap\r\n\t\t\t\t&:last-child {\r\n\t\t\t\t\tz-index: 1; // push these down, so that hover works on submenus\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:nth-last-child(2) {\r\n\t\t\t\t\tz-index: 2;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:nth-last-child(3) {\r\n\t\t\t\t\tz-index: 3;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&:nth-last-child(4) {\r\n\t\t\t\t\tz-index: 4;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ta {\r\n\t\t\t\t\tfont-family: $font_nav;\r\n\t\t\t\t\t@include links($navcolor, $navcolor_over, linkDecNo, linkDecUnderline);\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\tpadding: 10px 15px;\r\n\t\t\t\t\tfont-size: 17px;\r\n\t\t\t\t\twhite-space: nowrap;\r\n\t\t\t\t\ttext-transform: uppercase;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.current-menu-item > a {\r\n\t\t\t\t\ttext-decoration: underline;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.menu-item-has-children {\r\n\t\t\t\t\t> a {\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tpadding-right: 20px; // Make room for the arrow\r\n\r\n\t\t\t\t\t\t&::after {\r\n\t\t\t\t\t\t\tcontent: \" ▾\";\r\n\t\t\t\t\t\t\tfont-size: 0.8em;\r\n\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\tright: 5px;\r\n\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\t\t\tcolor: inherit;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tul.sub-menu {\r\n\t\t\t\t\t\tdisplay: none; // Hide submenus by default\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\ttop: 100%;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\tz-index: 999;\r\n\t\t\t\t\t\tbackground: $headerBackground;\r\n\t\t\t\t\t\tmin-width: 200px;\r\n\t\t\t\t\t\t//box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);\r\n\t\t\t\t\t\tpadding: 5px 0;\r\n\r\n\t\t\t\t\t\tli {\r\n\t\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\t\tposition: relative;\r\n\r\n\t\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\t\tpadding: 8px 15px;\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// Third level menu items with children\r\n\t\t\t\t\t\t\t&.menu-item-has-children {\r\n\t\t\t\t\t\t\t\t> a::after {\r\n\t\t\t\t\t\t\t\t\tcontent: \" ▸\"; // Right-pointing arrow for submenu items\r\n\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\tright: 10px;\r\n\t\t\t\t\t\t\t\t\ttop: 50%;\r\n\t\t\t\t\t\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t// Third level menu\r\n\t\t\t\t\t\t\t\t> ul.sub-menu {\r\n\t\t\t\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\t\t\t\ttop: 0;\r\n\t\t\t\t\t\t\t\t\tleft: 100%; // Position to the right of parent menu\r\n\t\t\t\t\t\t\t\t\tmin-width: 200px;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\t&:hover > ul.sub-menu {\r\n\t\t\t\t\t\t\t\t\tdisplay: block; // Show third level on hover\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&:hover > ul.sub-menu {\r\n\t\t\t\t\t\tdisplay: block; // Show submenu on hover\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t& > li:not(:last-child)::after {\r\n\t\t\t\tcontent: \"•\";\r\n\t\t\t\tdisplay: inline-block;\r\n\t\t\t\tcolor: $navcolor;\r\n\t\t\t\tmargin: 0 5px;\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: -8px;\r\n\t\t\t\ttop: 42%;\r\n\t\t\t\ttransform: translateY(-50%);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n", "\r\nh1, .h1, .H1,\r\nh2, .h2, .H2,\r\nh3, .h3, .H3,\r\nh4, .h4, .H4,\r\nh5, .h5, .H5,\r\nh6, .h6, .H6, .headerfont {\r\n\ttext-rendering: optimizelegibility;\r\n\r\n\tfont-family: $font_header;\r\n\tfont-weight: $font_header_weight;\r\n\ttext-transform: capitalize;\r\n\tcolor: $H1_color;\r\n\tclear: both;\r\n\r\n\ta {\r\n\t\ttext-decoration: none; /* removing text decoration from all headline links */\r\n\t\t@include links($H1_link, $H1_link_over !important, 'linkDecNo !important', linkDecNoImportant);\r\n\t}\r\n\r\n}\r\n\r\n\r\n//$H1_colorDkBg: $dkwhite;\r\n//$H1_linkDkBg: $dkwhite;\r\n//$H1_link_overDkBg: $ltgrey;\r\n//$fontColorDkBg: $palepink;\r\n//$linkcolorDkBg: $palepink;\r\n//$linkoverDkBg: $ltgrey;\r\n.darkbg {\r\n\th1, .h1, .H1,\r\n\th2, .h2, .H2,\r\n\th3, .h3, .H3,\r\n\th4, .h4, .H4,\r\n\th5, .h5, .H5,\r\n\th6, .h6, .H6, .headerfont {\r\n\t\tcolor: $H1_colorDkBg;\r\n\r\n\t\ta {\r\n\t\t\ttext-decoration: none; /* <-- removing text decoration from all headline links */\r\n\t\t\t@include links($H1_linkDkBg, $H1_link_overDkBg !important, linkDecNo, linkDecNo);\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\nh1, .h1, .H1,\r\nh2, .h2, .H2,\r\nh3, .h3, .H3,\r\nh4, .h4, .H4,\r\nh5, .h5, .H5,\r\nh6, .h6, .H6, .headerfont {\r\n\tmargin-right: 0;\r\n\tmargin-left: 0;\r\n\tmargin-top: $H1_topAlign;\r\n\tmargin-bottom: $H1_margin-bottom; // for all headings is a great approach because:\tIt scales proportionally with each heading's font size\tIt maintains consistent spacing relative to the text size\r\n\tline-height: $H1_line-height;\r\n}\r\n\r\n\r\nh1, .h1, .H1 {\r\n\tfont-size: $h1-size;\r\n}\r\n\r\nh2, .h2, .H2 {\r\n\tfont-size: $h2-size;\r\n}\r\n\r\nh3, .h3, .H3 {\r\n\tfont-size: $h3-size;\r\n}\r\n\r\nh4, .h4, .H4 {\r\n\tfont-size: $h4-size;\r\n}\r\n\r\nh5, .h5, .H5 {\r\n\tfont-size: $h5-size;\r\n}\r\n\r\nh6, .h6, .H6 {\r\n\tfont-size: $h6-size;\r\n}\r\n", "// yes, we use this for beaver builder buttons, etc.\r\n//<editor-fold desc=\"-- @mixin d_buttons -- \">\r\n\r\n@mixin d_buttons {\r\n\tbackground: $red !important;\r\n\tcolor: $dkwhite !important;\r\n\tfont-size: 14px;\r\n\tline-height: 16px;\r\n\tborder-radius: 4px;\r\n\tborder: 1px solid $grey;\r\n\tpadding: 10px 20px 11px;\r\n\tspan,\r\n\t.fl-button-icon {\r\n\t\tcolor: $white !important;\r\n\t}\r\n\t// .fl-button {\r\n\t//\t@include d_buttons;\r\n\t//}\r\n}\r\n\r\n.fl-button {\r\n\t@include d_buttons; // i guess\r\n}\r\n\r\n//</editor-fold> -- @mixin d_buttons --\r\n\r\n\r\n//http://galp.in/blog/2011/08/02/the-ui-guide-part-1-buttons/\r\n/* BUTTON DEFAULTS We're gonna use a placeholder selector here so we can use common styles. We then use this\r\nto load up the defaults in all our buttons.   Here's a quick video to show how it works:http://www.youtube.com/watch?v=hwdVpKiJzac  */\r\n\r\n%btn {\r\n\tdisplay: inline-block;\r\n\tposition: relative;\r\n\ttext-decoration: none;\r\n\tcolor: $white;\r\n\tfont-size: 0.9em;\r\n\tline-height: 34px;\r\n\tfont-weight: normal;\r\n\tpadding: 0 24px;\r\n\tborder-radius: 4px;\r\n\tborder: 0;\r\n\tcursor: pointer;\r\n\ttransition: background-color 0.24s ease-in-out;\r\n\r\n\t// hovering on the btn\r\n\t&:hover, &:focus {\r\n\t\tcolor: $white;\r\n\t\ttext-decoration: none;\r\n\t\toutline: none;\r\n\t}\r\n\r\n\t// clicking on the button\r\n\t&:active {\r\n\t\ttop: 1px; // adds a tiny hop to the click\r\n\t}\r\n\r\n}\r\n\r\n// An example button.You can use this example if you want. Just replace all the variables and it will create a button dependant on those variables.\r\n.blue-btn {\r\n\t@extend %btn; // here we load the btn defaults\r\n\tbackground-color: $blue;\r\n\r\n\t&:hover,\r\n\t&:focus {\r\n\t\tbackground-color: darken($blue, 4%);\r\n\t}\r\n\r\n\t&:active {\r\n\t\tbackground-color: darken($blue, 5%);\r\n\t}\r\n\r\n}\r\n\r\n\r\n\r\n\r\n", "//<div id=\"footers_container\" class=\"cf\">\r\n//\t<div id=\"mainfooter_container\">\r\n//\t\t<div id=\"mainfooter_wrapper\" class=\"wrapper\">\r\n//\t\t\t<div id=\"search_and_copyright\">\r\n#footers_container {\r\n\t@include container;\r\n\tbackground: $footerBackground;\r\n\tmargin-top: $wrapper_padding;\r\n\tcolor: $fontColor_footer;\r\n\tmargin-bottom: ($wrapper_padding * 2);\r\n\r\n\t#mainfooter_container {\r\n\t\t@include container;\r\n\r\n\t\t#mainfooter_wrapper {\r\n\t\t\t@include wrapper;\r\n\t\t\tpadding-top: ($wrapper_padding / 2) !important;\r\n\t\t\tpadding-bottom: ($wrapper_padding / 2) !important;\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-gap: 2vw;\r\n\t\t\tgrid-template-columns: (repeat(2, 1fr));\r\n\t\t\talign-items: center; // This vertically centers the content\r\n\r\n\t\t\ta {\r\n\t\t\t\t@include links($fontColor_link_footer, $fontColor_link_footer_over, linkDecNo, linkDecNo);\r\n\t\t\t}\r\n\r\n\t\t\t#left {\r\n\t\t\t\ttext-align: left;\r\n\t\t\t}\r\n\r\n\t\t\t#right {\r\n\t\t\t\ttext-align: right;\r\n\t\t\t\t// for search boxes or whathaveyou\r\n\t\t\t\tform {\r\n\t\t\t\t\tdisplay: inline-block;\r\n\r\n\t\t\t\t\tinput {\r\n\t\t\t\t\t\tfloat: right;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlabel {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n}\r\n\r\nbody.is_front_page,\r\nbody.fullwidth {\r\n\t#footers_container {\r\n\t\tmargin-top: -1px !important; // need a fuckin px\r\n\t}\r\n}\r\n\r\n", "// this gets hidden, during wprm\r\n//<editor-fold desc=\"-- header structure -- \">\r\n//<body >\r\n//<div id=\"headers_container\">\r\n//\t<div id=\"mainheader_container\" class=\"\">\r\n//\t\t<div id=\"mainheader_wrapper\" class=\"cf\">\r\n//\t\t\t<div id=\"logo-and-text\">\r\n//\t\t\t\t<div id=\"text\">\r\n//\t\t\t\t\t<div id=\"site-title-text\"><a href=\"/\"></div>\r\n//\t\t\t\t\t<div id=\"site-description\"></div>\r\n//\t\t\t\t</div>\r\n//\t\t\t</div>\r\n//\t\t\t<div id=\"menu\">\r\n//\t\t\t\t<nav id=\"main-menu\"></nav>\r\n//\t\t\t</div>\r\n//\t\t</div>\r\n//\t</div>\r\n//</div>\r\n//</editor-fold> -- header structure --\r\n\r\n#headers_container {\r\n\twidth: 100%;\r\n\tbackground-color: $headerBackground;\r\n\r\n\t#mainheader_container {\r\n\t\t//@media screen and (max-width: ($wrapperpx + $wrapperpxBuffer)) {\r\n\t\t//\tpadding: 0 $wrapper_padding;\r\n\t\t//}\r\n\t\t@include container;\r\n\r\n\t\t#mainheader_wrapper {\r\n\t\t\t//max-width: $wrapperpx;\r\n\t\t\t//margin: 0 auto;\r\n\t\t\t@include wrapper;\r\n\r\n\t\t\t#logo-and-text {\r\n\t\t\t\tfloat: left;\r\n\t\t\t\tmargin: 0;\r\n\r\n\t\t\t\ta {\r\n\t\t\t\t\t@include links($fontColor_link_header, $fontColor_link_header_over, linkDecNo, linkDecNo);\r\n\t\t\t\t\ttext-decoration: none !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t#menu {\r\n\t\t\t\tfloat: right;\r\n\t\t\t\tmargin: 16px 0 0 0 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n", "//\r\n//\r\nbody {\r\n\t#body_container {\r\n\t\t// no this has no top margin.@include container;\r\n\t\tmargin: $wrapper_padding auto;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 2000px;\r\n\r\n\t\t#body_wrapper {\r\n\t\t\t@include wrapper;\r\n\t\t}\r\n\t}\r\n\r\n\t&.fullwidth #body_container {\r\n\t\tmargin: 0;\r\n\r\n\t\t#body_wrapper {\r\n\t\t\tmax-width: 100% !important;\r\n\t\t\tpadding: 0 !important;\r\n\r\n\t\t\t@include up_to_wrapper {\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t}\r\n\t\t\t@include up_to_ipad {\r\n\t\t\t\tpadding: 0 !important;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// idk if this really does what you want.  just cuz there will likely be a single div.. idk.\r\n\t\t.entry-content,\r\n\t\t#post_wrapper,\r\n\t\tdiv#content {\r\n\t\t\t& > div:last-child {\r\n\t\t\t\tpadding-bottom: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n// so basically, if you have it on fullwidth, you'll be using beaver, which has its own wrapper max width and stuff.\r\n// So, no padding on any of this.\r\n// if you have it on fullwidth, without using beaver,\r\n// you'll need to set width on  <div class='content' or whatever.\r\n//body.fullwidth {\r\n//\t#body_container {\r\n//\t\tmargin-bottom: 0;\r\n//\t\tmargin-top: 0;\r\n//\r\n//\t\t#body_wrapper {\r\n//\t\t\tmax-width: 100% !important;\r\n//\t\t\tpadding: 0 !important;\r\n//\r\n//\t\t\t@include up_to_wrapper {\r\n//\t\t\t\tpadding: 0 !important;\r\n//\t\t\t}\r\n//\t\t\t@include up_to_ipad {\r\n//\t\t\t\tpadding: 0 !important;\r\n//\t\t\t}\r\n//\t\t}\r\n//\t}\r\n//\r\n//\tdiv#content > div:last-child {\r\n//\t\tpadding-bottom: 0;\r\n//\t}\r\n//}\r\n//\r\n\r\n\r\n", "//<editor-fold desc=\"-- zero out margins and padding -- \">\r\n#body_wrapper {\r\n\t.fl-builder-module-template {\r\n\t\tpadding: 0; // where a module is imported with shortcode and that's all there is.\r\n\t}\r\n\r\n\t.fl-builder-content-primary { // maybe any .fl-builder-content  but prolly just the primary\r\n\t\t& > .fl-row { //, & > .fl-row-content\r\n\t\t\tmargin-bottom: $wrapper_padding;\r\n\r\n\t\t\t// ech,dont do this. &:last-child { margin-bottom: 0; }\r\n\r\n\t\t\t.fl-row-content-wrap { // this is crap i think: .fl-row-layout-full-fixed .fl-row-fixed-width > .fl-col-group {\r\n\t\t\t\tmargin-left: 0 !important;\r\n\t\t\t\tmargin-right: 0 !important;\r\n\t\t\t\tpadding: 0 !important;\r\n\r\n\t\t\t\t// remove padding and margin all to Zero\r\n\t\t\t\t.fl-col {\r\n\t\t\t\t\tpadding: 0 ($wrapper_padding / 2);\r\n\r\n\t\t\t\t\t// yes, this makes cols work, when there are multiple\r\n\t\t\t\t\t// but also, on only one, both paddings are 0 (cuz its first and last)\r\n\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\tpadding-left: 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t&:last-child {\r\n\t\t\t\t\t\tpadding-right: 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tp:last-child {\r\n\t\t\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.fl-post-grid {\r\n\t\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\t\tmargin-right: 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// ok, this is for when columns stack.  They stack at the 'small' breakpoint.\r\n\t\t\t\t@media (max-width: $small_max-width) {\r\n\t\t\t\t\t& .fl-col-small:not(.fl-col-small-full-width) {\r\n\t\t\t\t\t\tmax-width: 100%;\r\n\t\t\t\t\t\tvisibility: visible;\r\n\t\t\t\t\t\tpadding: 0;\r\n\r\n\t\t\t\t\t\tmargin: ($wrapper_padding * 4) 0;\r\n\r\n\t\t\t\t\t\t&:first-child {\r\n\t\t\t\t\t\t\tmargin: 0 0 ($wrapper_padding * 4) 0;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// wrapper stuff.  yes, i think this is good.  fl-row-full-width will be fullwidth on its own\r\n\t.fl-row.fl-row-fixed-width {\r\n\t\tpadding: 0;\r\n\r\n\t\t// leave this\r\n\t\t.fl-row-fixed-width { // this is for inner rows, like  .fl-row-fixed-width .fl-row-fixed-width {}\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n}\r\n//</editor-fold> -- zero out margins and padding --\r\n\r\n\r\n// if you have a row on a fullwidth page, but you need the content to be padded, then use fullbutpadded on the row.\r\n//<editor-fold desc=\"-- fullbutpadded -- \">\r\nbody.fullwidth div.fl-row.fl-row-fixed-width,\r\nbody.fullwidth #body_container #body_wrapper div.fl-row.fl-row-full-width.fullbutpadded div.fl-row-content-wrap {\r\n\t@include up_to_wrapper {\r\n\t\tpadding: 0 $wrapper_padding !important;\r\n\t}\r\n}\r\n//</editor-fold> -- fullbutpadded --\r\n\r\n//<editor-fold desc=\"-- yes, we need this margin zero -- \">\r\n\r\n.fl-builder-content,\r\n.fl-builder-template,\r\n.fl-builder-row-template {\r\n\t.fl-module-content {\r\n\t\tmargin: 0 !important;\r\n\t}\r\n}\r\n//</editor-fold> -- yes, we need this margin zero --\r\n\r\n\r\n//<editor-fold desc=\"-- line heights, font sizes etc -- \">\r\ndiv.fl-module-post-grid {\r\n\tdiv.fl-post-column {\r\n\t\t.fl-post-grid-text {\r\n\t\t\tpadding: 1vw !important;\r\n\r\n\t\t\t.fl-post-grid-title {\r\n\t\t\t\t@include fluid-type(22px, 18px);\r\n\t\t\t\tline-height: 1.2 !important;\r\n\r\n\t\t\t\t&, a {\r\n\t\t\t\t\ttext-decoration: none !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.fl-post-grid-meta,\r\n\t\t\t.fl-post-grid-content {\r\n\t\t\t\t&, p, span {\r\n\t\t\t\t\t@include fluid-type(17px, 15px);\r\n\t\t\t\t\tline-height: 1.4 !important;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n//</editor-fold> -- line heights, font sizes etc --\r\n\r\n\r\n//better spacing for post grids\r\n//<editor-fold desc=\"-- mixins -- \">\r\n\r\n//$beaverbuilder_LargeMaxWidth: 1200px; //up_to_large {@media screen and (max-width: $beaverbuilder_LargeMaxWidth)\r\n//$beaverbuilder_LargeMinWidth: ($beaverbuilder_LargeMaxWidth + 1); //more_than_large {@media screen and (min-width: $beaverbuilder_LargeMinWidth)\r\n//\r\n//$beaverbuilder_MediumMaxWidth: 992px;\r\n//$beaverbuilder_MediumMinWidth: ($beaverbuilder_MediumMaxWidth + 1);\r\n//\r\n//$beaverbuilder_SmallMaxWidth: 768px;\r\n//$beaverbuilder_SmallMinWidth: ($beaverbuilder_SmallMaxWidth + 1);\r\n@mixin bb_up_to_large {\r\n\t@media screen and (max-width: $beaverbuilder_LargeMaxWidth) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin bb_more_than_large {\r\n\t@media screen and (min-width: $beaverbuilder_LargeMinWidth) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin bb_up_to_medium {\r\n\t@media screen and (max-width: $beaverbuilder_MediumMaxWidth) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin bb_more_than_medium {\r\n\t@media screen and (min-width: $beaverbuilder_MediumMinWidth) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin bb_up_to_small {\r\n\t@media screen and (max-width: $beaverbuilder_SmallMaxWidth) {\r\n\t\t@content;\r\n\t}\r\n}\r\n@mixin bb_more_than_small {\r\n\t@media screen and (min-width: $beaverbuilder_SmallMinWidth) {\r\n\t\t@content;\r\n\t}\r\n}\r\n//</editor-fold> -- mixins --\r\n\r\n//<editor-fold desc=\"-- padding for cols disabled. try grid -- \">\r\ndiv.fl-module-post-gridXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX { //, div.fl-post-grid.masonry\r\n\tdiv.fl-post-column { //, div.fl-post-grid-post\r\n\t\t//with 2% margin,\r\n\t\t//5 col\t18.39\r\n\t\t//4 col\t22.99\r\n\t\t//3 col\t31.32\r\n\t\t//2 col\t47.95\r\n\r\n\t\tpadding: 0 !important;\r\n\t\twidth: 18.39% !important;\r\n\t\tmargin-bottom: 2%;\r\n\t\tmargin-right: 2%;\r\n\r\n\t\t&:nth-child(5n) {\r\n\t\t\tmargin-right: 0 !important;\r\n\t\t}\r\n\r\n\r\n\t\t@include bb_up_to_large { // 4 cols\r\n\t\t\twidth: 22.99% !important;\r\n\t\t\t&:nth-child(4n) {\r\n\t\t\t\tmargin-right: 0 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@include bb_up_to_medium { // 3 cols\r\n\t\t\twidth: 31.32% !important;\r\n\t\t\t&:nth-child(3n) {\r\n\t\t\t\tmargin-right: 0 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t\t@include bb_up_to_small { // 2 cols\r\n\t\t\twidth: 47.95% !important;\r\n\t\t\t&:nth-child(2n) {\r\n\t\t\t\tmargin-right: 0 !important;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n//</editor-fold> -- padding for cols --\r\n\r\ndiv.fl-post-grid { //, div.fl-post-grid.masonry\r\n\t@include gridColumns5432;\r\n\tgap: 1.5vw;\r\n\tpadding: 0 !important;\r\n\tmargin: 0 auto !important;\r\n\r\n\r\n\tdiv.fl-post-column {\r\n\t\twidth: auto;\r\n\t\tmin-width: 0;\r\n\t\tpadding: 0 !important;\r\n\t\tmargin: 0 !important;\r\n\t}\r\n\r\n\t&:before,\r\n\t&:after,\r\n\t&:not([data-accepts]):before,\r\n\t&:not([data-accepts]):after {\r\n\t\t// i think this was just for clearfix.  but floats are for chumps.\r\n\t\tdisplay: none !important;\r\n\t}\r\n\r\n}\r\n\r\n\r\n.fl-module-heading .fl-heading {\r\n\tmargin-bottom: ($wrapper_padding / 2) !important;\r\n}\r\n", "// these are set in the d_gallery_create_2024()\r\n.d_createGallery {\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n\twidth: 100%;\r\n\r\n\t.d_createGallery__item {\r\n\t\tposition: relative;\r\n\t\tdisplay: block;\r\n\t\twidth: 100%;\r\n\t\tpadding: 0;\r\n\t\tmargin: 0;\r\n\r\n\t\timg {\r\n\t\t\tdisplay: block;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\timg, p, div, a, span {\r\n\t\t\tmargin: 0 !important;\r\n\t\t\tpadding: 0;\r\n\t\t}\r\n\t}\r\n\r\n}\r\n// grid dddddddddddddddddddddddddddddddd\r\n#d_createGallery.columns {\r\n\tdisplay: grid;\r\n\tgap: 24px;\r\n\r\n\t//<editor-fold desc=\"-- fold -- \">\r\n\r\n\t&.portraits {\r\n\t\t.d_createGallery__item {\r\n\t\t\ta {\r\n\t\t\t\taspect-ratio: 4 / 5; /* Set the desired aspect ratio */\r\n\t\t\t\t//overflow: hidden;\r\n\t\t\t\tdisplay: block;\r\n\r\n\t\t\t\timg {\r\n\t\t\t\t\theight: 100%; // Force image to take full height\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\tobject-fit: cover; // Scale and crop to fill container\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t&.cols_6 {\r\n\t\tgrid-template-columns: repeat(6, 1fr);\r\n\t}\r\n\r\n\t&.cols_5 {\r\n\t\tgrid-template-columns: repeat(5, 1fr);\r\n\t}\r\n\r\n\t&.cols_4 {\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t}\r\n\r\n\t&.cols_3 {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\r\n\t@include up_to_small {\r\n\t\tgrid-template-columns: repeat(2, 1fr) !important; // Override any other column settings\r\n\t\tgap: 1vw !important; // Reduce gap on small screens\r\n\t}\r\n\t//</editor-fold> -- fold --\r\n}\r\n\r\n// masonry mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm\r\n.d_createGallery.masonry {\r\n\t$d_gallery_masonry_gutter: 1.5% !default; //you can override this\r\n\r\n\r\n\t#masonrygutter {\r\n\t\twidth: $d_gallery_masonry_gutter;\r\n\t}\r\n\r\n\r\n\t.d_createGallery__item {\r\n\t\tmargin: 0 0 $d_gallery_masonry_gutter 0;\r\n\r\n\t\t@for $i from 2 through 7 {\r\n\t\t\t&.cols_#{$i} {\r\n\t\t\t\tmax-width: calc((100% - (#{$d_gallery_masonry_gutter} * (#{$i} - 1))) / #{$i});\r\n\t\t\t}\r\n\t\t}\r\n\t\t@include up_to_small { //for small screens bring 4-7 cols to 3\r\n\t\t\t@for $i from 4 through 7 {\r\n\t\t\t\t&.cols_#{$i} {\r\n\t\t\t\t\tmax-width: calc((100% - (#{$d_gallery_masonry_gutter} * (2))) / (3));\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n.d_createGallery.hoverup {\r\n\t.d_createGallery__item {\r\n\r\n\t\toverflow: hidden; //for the hoverup\r\n\t\t//<editor-fold desc=\"-- d_createGallery__title -- \">\r\n\t\t.d_createGallery__title {\r\n\t\t\ttext-align: left;\r\n\t\t\tposition: absolute;\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding: 2% 1% 3% 3% !important;\r\n\t\t\tfont-size: 13px;\r\n\t\t\tbackground-color: rgba(255, 255, 255, .85);\r\n\t\t\tcolor: $dkblue;\r\n\t\t\topacity: 0;\r\n\t\t\tbottom: -42px;\r\n\t\t\ttransition: all 0.6s ease;\r\n\t\t\t@include up_to_small {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&:hover .d_createGallery__title {\r\n\t\t\topacity: 1;\r\n\t\t\tbottom: -1px;\r\n\t\t}\r\n\r\n\t\t.d_createGallery__caption, .d_createGallery__description {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t//</editor-fold> -- d_createGallery__title --\r\n\t}\r\n\r\n\t//<editor-fold desc=\"-- the info ? icon -- \">\r\n\t.d_createGallery__item.has_caption:before {\r\n\t\tcontent: \"\\1F50D\"; // maginifiing glass, this is css code https://symbl.cc/en/1F50D/  https://www.w3schools.com/cssref/css_entities.php\r\n\t\tcolor: black;\r\n\t\tfont-size: clamp(11px, 2vw, 13px); //min normal max\r\n\t\tline-height: normal;\r\n\t\ttext-align: center;\r\n\r\n\t\tbackground: rgba(255, 255, 255, 0.55);\r\n\t\t@include border-radius(2vw);\r\n\t\twidth: 3vw;\r\n\t\theight: 3vw;\r\n\t\tmax-width: 20px;\r\n\t\tmax-height: 20px;\r\n\r\n\t\ttransition: all 0.6s ease;\r\n\t\topacity: 0.75;\r\n\r\n\t\tposition: absolute;\r\n\t\tbottom: 1px;\r\n\t\tright: 1px;\r\n\t\t@include up_to_small {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t}\r\n\r\n\t.d_createGallery__item.has_caption:hover:before {\r\n\t\topacity: 0; // if they are hovering, theyre looking at the title, so hide the ?\r\n\t}\r\n\r\n\t//</editor-fold> -- the info ? icon --\r\n}\r\n\r\n.d_createGallery.under {\r\n\t&.columns {\r\n\t\tgap: 4vw 2vw !important; // Increase row gap for the \"under\" layout with columns\r\n\t}\r\n\r\n\t.d_createGallery__item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-bottom: 1vw; // Add bottom margin\r\n\r\n\t\t.d_createGallery__title {\r\n\t\t\tmargin-top: 0 !important;\r\n\t\t\tpadding: 1.5vw 1vw 0 !important;\r\n\t\t\tcolor: $dkblue;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tfont-family: $serif;\r\n\r\n\t\t\tfont-size: 27px;\r\n\t\t\tz-index: 2; // Ensure title appears above other elements\r\n\t\t}\r\n\r\n\t\t.d_createGallery__title,\r\n\t\t.d_createGallery__caption,\r\n\t\t.d_createGallery__description {\r\n\t\t\tline-height: 1;\r\n\t\t\twidth: 100%;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t.d_createGallery__caption.titleandcaps {\r\n\t\t\tmargin-top: .5vw;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n//   dont.\r\n//\r\n// hide the captions sssssssssssssssssssssssssssss sssssssssssssssssssssssssssss sssssssssssssssssssssssssssss\r\n// hide the captions ssssssssssssssssssssssssssss sssssssssssssssssssssssssssss ssssssssssssssssssssssssssssss\r\n//body.is_mobile {\r\n//\r\n//\t.d_createGallery__item:hover div.d_createGallery__item_hover,\r\n//\t.d_createGallery__item div.d_createGallery__item_hover,\r\n//\t.d_createGallery__item.has_caption:before,\r\n//\t.d_createGallery__title {\r\n//\t\t///* Lets just not show it on mobile, the caption will be in the popup\t*/\r\n//\t\t//display: block !important;\r\n//\r\n//\t}\r\n//}\r\n\r\n\r\n//gallery of galleries  gallery of galleries sssssssssssssssssssssssssssss sssssssssssssssssssssssssssss sssssssssssssssssssssssssssss\r\n//gallery of galleries  gallery of galleries  gallery of galleries sssssssssssssssssssssssssssss sssssssssssssssssssssssssssss sssssssssssssssssssssssssssss\r\n//gallery of galleries  gallery of galleries  gallery of galleries sssssssssssssssssssssssssssss sssssssssssssssssssssssssssss\r\n.show_d_gallery_of_galleries__each {\r\n\tfloat: left;\r\n\twidth: 48%;\r\n\tmargin-bottom: 4%;\r\n\tmargin-right: 4%;\r\n}\r\n\r\n.show_d_gallery_of_galleries__each .post-img img {\r\n\twidth: 100%;\r\n}\r\n\r\n.show_d_gallery_of_galleries__each:nth-of-type(2n+2) {\r\n\tmargin-right: 0;\r\n}\r\n\r\n@media screen and (max-width: 480px) {\r\n\t.show_d_gallery_of_galleries__each {\r\n\t\tfloat: none;\r\n\t\twidth: 100%;\r\n\t\tmax-width: 100%;\r\n\t\tmargin-bottom: 4% !important;\r\n\t\tmargin-right: 0 !important;\r\n\t}\r\n}\r\n\r\n", "// this is taken from glightbox dist/css\r\n//we will import this from the themes scss\r\n\r\n.gslide {\r\n\t.ginner-container {\r\n\t\t&.desc-bottom .gslide-image img {\r\n\t\t\twidth: auto;\r\n\t\t}\r\n\t}\r\n\r\n\t.gslide-description {\r\n\t\tposition: relative;\r\n\t\t-webkit-box-flex: 1;\r\n\t\t-ms-flex: 1 0 100%;\r\n\t\tflex: 1 0 100%;\r\n\r\n\t\t&.description-bottom,\r\n\t\t&.description-top {\r\n\t\t\tmargin: 0 auto;\r\n\t\t\twidth: 100%;\r\n\t\t}\r\n\r\n\t\tp {\r\n\t\t\tmargin-bottom: 12px;\r\n\r\n\t\t\t&:last-child {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t.gslide-image {\r\n\t\t-webkit-box-align: center;\r\n\t\t-ms-flex-align: center;\r\n\t\talign-items: center;\r\n\r\n\t\timg {\r\n\t\t\tmax-height: 100vh;\r\n\t\t\tdisplay: block;\r\n\t\t\tpadding: 0;\r\n\t\t\tfloat: none;\r\n\t\t\toutline: none;\r\n\t\t\tborder: none;\r\n\t\t\t-webkit-user-select: none;\r\n\t\t\t-moz-user-select: none;\r\n\t\t\t-ms-user-select: none;\r\n\t\t\tuser-select: none;\r\n\t\t\tmax-width: 100vw;\r\n\t\t\twidth: auto;\r\n\t\t\theight: auto;\r\n\t\t\t-o-object-fit: cover;\r\n\t\t\tobject-fit: cover;\r\n\t\t\t-ms-touch-action: none;\r\n\t\t\ttouch-action: none;\r\n\t\t\tmargin: auto;\r\n\t\t\tmin-width: 200px;\r\n\t\t}\r\n\t}\r\n\r\n}\r\n\r\n\r\n.zoomed .gslide-description {\r\n\tdisplay: none;\r\n}\r\n\r\n// this is put by the js, to the body class\r\nbody.glightbox-mobile {\r\n\t.glightbox-container {\r\n\t\t.gslide-description {\r\n\t\t\theight: auto !important;\r\n\t\t\twidth: 100%;\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 2px;\r\n\t\t\tpadding: 2vw 0;\r\n\t\t\tmax-width: 100vw !important;\r\n\t\t\t-webkit-box-ordinal-group: 3 !important;\r\n\t\t\t-ms-flex-order: 2 !important;\r\n\t\t\torder: 2 !important;\r\n\t\t\tmax-height: 78vh;\r\n\t\t\toverflow: auto !important;\r\n\t\t\t//background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, 0)), to(rgba(0, 0, 0, 0.75)));\r\n\t\t\tbackground: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.75) 100%);\r\n\t\t\t-webkit-transition: opacity 0.3s linear;\r\n\t\t\ttransition: opacity 0.3s linear;\r\n\t\t\tpadding-bottom: 50px;\r\n\t\t}\r\n\r\n\t\t.gslide-title {\r\n\t\t\tcolor: $fontcolor;\r\n\t\t\tfont-size: 1em;\r\n\t\t}\r\n\r\n\t\t.gslide-desc {\r\n\t\t\tcolor: rgba($fontcolor, 0.8);\r\n\r\n\t\t\ta {\r\n\t\t\t\tcolor: $fontcolor;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\r\n\t\t\t.desc-more {\r\n\t\t\t\tcolor: $fontcolor;\r\n\t\t\t\topacity: 0.4;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t.goverlay {\r\n\t\tbackground: #000;\r\n\t}\r\n\r\n}\r\n.gdesc-open .gdesc-inner {\r\n\tpadding-bottom: 30px;\r\n}\r\n\r\n\r\n.glightbox-container {\r\n\t&.inactive {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tz-index: 999999 !important;\r\n\toverflow: hidden;\r\n\t-ms-touch-action: none;\r\n\ttouch-action: none;\r\n\t-webkit-text-size-adjust: 100%;\r\n\t-moz-text-size-adjust: 100%;\r\n\t-ms-text-size-adjust: 100%;\r\n\ttext-size-adjust: 100%;\r\n\t-webkit-backface-visibility: hidden;\r\n\tbackface-visibility: hidden;\r\n\toutline: none;\r\n\r\n\t.gcontainer {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tz-index: 9999;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.gslider {\r\n\t\t-webkit-transition: -webkit-transform 0.4s ease;\r\n\t\ttransition: -webkit-transform 0.4s ease;\r\n\t\ttransition: transform 0.4s ease;\r\n\t\ttransition: transform 0.4s ease, -webkit-transform 0.4s ease;\r\n\t\theight: 100%;\r\n\t\tleft: 0;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\tdisplay: -webkit-box !important;\r\n\t\tdisplay: -ms-flexbox !important;\r\n\t\tdisplay: flex !important;\r\n\t\t-webkit-box-pack: center;\r\n\t\t-ms-flex-pack: center;\r\n\t\tjustify-content: center;\r\n\t\t-webkit-box-align: center;\r\n\t\t-ms-flex-align: center;\r\n\t\talign-items: center;\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\r\n\t.gslide {\r\n\t\twidth: 100%;\r\n\t\tposition: absolute;\r\n\r\n\t\t-webkit-user-select: none;\r\n\t\t-moz-user-select: none;\r\n\t\t-ms-user-select: none;\r\n\t\tuser-select: none;\r\n\r\n\t\tdisplay: -webkit-box;\r\n\t\tdisplay: -ms-flexbox;\r\n\t\tdisplay: flex;\r\n\r\n\t\t-webkit-box-align: center;\r\n\t\t-ms-flex-align: center;\r\n\t\talign-items: center;\r\n\r\n\t\t-webkit-box-pack: center;\r\n\t\t-ms-flex-pack: center;\r\n\t\tjustify-content: center;\r\n\t\topacity: 0;\r\n\t}\r\n\r\n\t.gslide.current {\r\n\t\topacity: 1;\r\n\t\tz-index: 99999;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.gslide.prev {\r\n\t\topacity: 1;\r\n\t\tz-index: 9999;\r\n\t}\r\n\r\n\t.gslide-inner-content {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.ginner-container {\r\n\t\tposition: relative;\r\n\t\twidth: 100%;\r\n\t\tdisplay: -webkit-box;\r\n\t\tdisplay: -ms-flexbox;\r\n\t\tdisplay: flex;\r\n\t\t-webkit-box-pack: center;\r\n\t\t-ms-flex-pack: center;\r\n\t\tjustify-content: center;\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-box-direction: normal;\r\n\t\t-ms-flex-direction: column;\r\n\t\tflex-direction: column;\r\n\t\tmax-width: 100%;\r\n\t\tmargin: auto;\r\n\t\theight: 100vh;\r\n\t}\r\n\r\n\t.ginner-container.gvideo-container {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.ginner-container.desc-bottom,\r\n\t.ginner-container.desc-top {\r\n\t\t-webkit-box-orient: vertical;\r\n\t\t-webkit-box-direction: normal;\r\n\t\t-ms-flex-direction: column;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.ginner-container.desc-left,\r\n\t.ginner-container.desc-right {\r\n\t\tmax-width: 100% !important;\r\n\t}\r\n\r\n}\r\n\r\n\r\n.gslide iframe,\r\n.gslide video {\r\n\toutline: none !important;\r\n\tborder: none;\r\n\tmin-height: 165px;\r\n\t-webkit-overflow-scrolling: touch;\r\n\t-ms-touch-action: auto;\r\n\ttouch-action: auto;\r\n}\r\n\r\n.gslide:not(.current) {\r\n\tpointer-events: none;\r\n}\r\n\r\n\r\n.desc-top .gslide-image img,\r\n.desc-left .gslide-image img,\r\n.desc-right .gslide-image img {\r\n\twidth: auto;\r\n\tmax-width: 100%;\r\n}\r\n\r\n.gslide-image img.zoomable {\r\n\tposition: relative;\r\n}\r\n\r\n.gslide-image img.dragging {\r\n\t//\tcursor: -webkit-grabbing !important;\r\n\tcursor: grabbing !important;\r\n\t-webkit-transition: none;\r\n\ttransition: none;\r\n}\r\n\r\n.gslide-video {\r\n\tposition: relative;\r\n\tmax-width: 100vh;\r\n\twidth: 100% !important;\r\n}\r\n\r\n.gslide-video .plyr__poster-enabled.plyr--loading .plyr__poster {\r\n\tdisplay: none;\r\n}\r\n\r\n.gslide-video .gvideo-wrapper {\r\n\twidth: 100%;\r\n\t/* max-width: 160vmin; */\r\n\tmargin: auto;\r\n}\r\n\r\n.gslide-video::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tbackground: rgba(255, 0, 0, 0.34);\r\n\tdisplay: none;\r\n}\r\n\r\n.gslide-video.playing::before {\r\n\tdisplay: none;\r\n}\r\n\r\n.gslide-video.fullscreen {\r\n\tmax-width: 100% !important;\r\n\tmin-width: 100%;\r\n\theight: 75vh;\r\n}\r\n\r\n.gslide-video.fullscreen video {\r\n\tmax-width: 100% !important;\r\n\twidth: 100% !important;\r\n}\r\n\r\n.gslide-inline {\r\n\tbackground: #fff;\r\n\ttext-align: left;\r\n\tmax-height: calc(100vh - 40px);\r\n\toverflow: auto;\r\n\tmax-width: 100%;\r\n\tmargin: auto;\r\n}\r\n\r\n.gslide-inline .ginlined-content {\r\n\tpadding: 20px;\r\n\twidth: 100%;\r\n}\r\n\r\n.gslide-inline .dragging {\r\n\t//cursor: -webkit-grabbing !important;\r\n\tcursor: grabbing !important;\r\n\t-webkit-transition: none;\r\n\ttransition: none;\r\n}\r\n\r\n.ginlined-content {\r\n\toverflow: auto;\r\n\tdisplay: block !important;\r\n\topacity: 1;\r\n}\r\n\r\n.gslide-external {\r\n\tdisplay: -webkit-box;\r\n\tdisplay: -ms-flexbox;\r\n\tdisplay: flex;\r\n\twidth: 100%;\r\n\tmin-width: 100%;\r\n\tbackground: #fff;\r\n\tpadding: 0;\r\n\toverflow: auto;\r\n\tmax-height: 75vh;\r\n\theight: 100%;\r\n}\r\n\r\n.gslide-media {\r\n\tdisplay: -webkit-box;\r\n\tdisplay: -ms-flexbox;\r\n\tdisplay: flex;\r\n\twidth: auto;\r\n}\r\n\r\n.zoomed .gslide-media {\r\n\t-webkit-box-shadow: none !important;\r\n\tbox-shadow: none !important;\r\n}\r\n\r\n.desc-top .gslide-media,\r\n.desc-bottom .gslide-media {\r\n\tmargin: 0 auto;\r\n\t-webkit-box-orient: vertical;\r\n\t-webkit-box-direction: normal;\r\n\t-ms-flex-direction: column;\r\n\tflex-direction: column;\r\n}\r\n\r\n\r\n.gslide-description.description-left,\r\n.gslide-description.description-right {\r\n\tmax-width: 100%;\r\n}\r\n\r\n\r\n.glightbox-button-hidden {\r\n\tdisplay: none;\r\n}\r\n\r\n\r\n.gdesc-open .gslide-media {\r\n\t-webkit-transition: opacity 0.5s ease;\r\n\ttransition: opacity 0.5s ease;\r\n\topacity: 0.4;\r\n}\r\n\r\n\r\n.gdesc-closed .gslide-media {\r\n\t-webkit-transition: opacity 0.5s ease;\r\n\ttransition: opacity 0.5s ease;\r\n\topacity: 1;\r\n}\r\n\r\n.greset {\r\n\t-webkit-transition: all 0.3s ease;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.gabsolute {\r\n\tposition: absolute;\r\n}\r\n\r\n.grelative {\r\n\tposition: relative;\r\n}\r\n\r\n.glightbox-desc {\r\n\tdisplay: none !important;\r\n}\r\n\r\n.glightbox-open {\r\n\toverflow: hidden;\r\n}\r\n\r\n.gloader {\r\n\theight: 25px;\r\n\twidth: 25px;\r\n\t-webkit-animation: lightboxLoader 0.8s infinite linear;\r\n\tanimation: lightboxLoader 0.8s infinite linear;\r\n\tborder: 2px solid #fff;\r\n\tborder-right-color: transparent;\r\n\tborder-radius: 50%;\r\n\tposition: absolute;\r\n\tdisplay: block;\r\n\tz-index: 9999;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tmargin: 0 auto;\r\n\ttop: 47%;\r\n}\r\n\r\n.goverlay {\r\n\twidth: 100%;\r\n\theight: calc(100vh + 1px);\r\n\tposition: fixed;\r\n\ttop: -1px;\r\n\tleft: 0;\r\n\tbackground: #000;\r\n\twill-change: opacity;\r\n}\r\n\r\n\r\n.gprev,\r\n.gnext,\r\n.gclose {\r\n\tz-index: 99999;\r\n\tcursor: pointer;\r\n\twidth: 26px;\r\n\theight: 44px;\r\n\tborder: none;\r\n\tdisplay: -webkit-box;\r\n\tdisplay: -ms-flexbox;\r\n\tdisplay: flex;\r\n\t-webkit-box-pack: center;\r\n\t-ms-flex-pack: center;\r\n\tjustify-content: center;\r\n\t-webkit-box-align: center;\r\n\t-ms-flex-align: center;\r\n\talign-items: center;\r\n\t-webkit-box-orient: vertical;\r\n\t-webkit-box-direction: normal;\r\n\t-ms-flex-direction: column;\r\n\tflex-direction: column;\r\n}\r\n\r\n.gprev svg,\r\n.gnext svg,\r\n.gclose svg {\r\n\tdisplay: block;\r\n\twidth: 25px;\r\n\theight: auto;\r\n\tmargin: 0;\r\n\tpadding: 0;\r\n}\r\n\r\n.gprev.disabled,\r\n.gnext.disabled,\r\n.gclose.disabled {\r\n\topacity: 0.1;\r\n}\r\n\r\n.gprev .garrow,\r\n.gnext .garrow,\r\n.gclose .garrow {\r\n\tstroke: #fff;\r\n}\r\n\r\n.gbtn.focused {\r\n\toutline: 2px solid #0f3d81;\r\n}\r\n\r\niframe.wait-autoplay {\r\n\topacity: 0;\r\n}\r\n\r\n.glightbox-closing .gnext,\r\n.glightbox-closing .gprev,\r\n.glightbox-closing .gclose {\r\n\topacity: 0 !important;\r\n}\r\n\r\n\r\n/*Skin */\r\n\r\n.glightbox-clean {\r\n\t//this is the whole title/desc div\r\n\t.gslide-description {\r\n\t\tbackground: $d_gallery_title_bg;\r\n\r\n\t\t.gdesc-inner {\r\n\t\t\tpadding: 2vw 2vw;\r\n\t\t\tbackground: #fff;\r\n\r\n\t\t\t.gslide-title {\r\n\t\t\t\tmargin-bottom: 1vw;\r\n\t\t\t\tcolor: $d_gallery_gslide-title;\r\n\t\t\t}\r\n\r\n\t\t\t//this is the actual desc\r\n\t\t\t.gslide-desc {\r\n\t\t\t\tcolor: $d_gallery_gslide-desc;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\t.gslide-video {\r\n\t\tbackground: $d_gallery_title_bg;\r\n\t}\r\n\r\n\t.gprev,\r\n\t.gnext,\r\n\t.gclose {\r\n\t\tbackground-color: rgba(0, 0, 0, 0.75);\r\n\t\tborder-radius: 4px;\r\n\t}\r\n\r\n\t.gprev path,\r\n\t.gnext path,\r\n\t.gclose path {\r\n\t\tfill: #fff;\r\n\t}\r\n\r\n\t.gprev {\r\n\t\tposition: absolute;\r\n\t\ttop: -100%;\r\n\t\tleft: 30px;\r\n\t\twidth: 40px;\r\n\t\theight: 50px;\r\n\t}\r\n\r\n\t.gnext {\r\n\t\tposition: absolute;\r\n\t\ttop: -100%;\r\n\t\tright: 30px;\r\n\t\twidth: 40px;\r\n\t\theight: 50px;\r\n\t}\r\n\r\n\t.gclose {\r\n\t\twidth: 35px;\r\n\t\theight: 35px;\r\n\t\ttop: 15px;\r\n\t\tright: 10px;\r\n\t\tposition: absolute;\r\n\t}\r\n\r\n\t.gclose svg {\r\n\t\twidth: 18px;\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t.gclose:hover {\r\n\t\topacity: 1;\r\n\t}\r\n\r\n}\r\n\r\n\r\n//<editor-fold desc=\"-- CSS Animations -- \">\r\n\r\n.gfadeIn {\r\n\t-webkit-animation: gfadeIn 0.5s ease;\r\n\tanimation: gfadeIn 0.5s ease;\r\n}\r\n\r\n.gfadeOut {\r\n\t-webkit-animation: gfadeOut 0.5s ease;\r\n\tanimation: gfadeOut 0.5s ease;\r\n}\r\n\r\n.gslideOutLeft {\r\n\t-webkit-animation: gslideOutLeft 0.3s ease;\r\n\tanimation: gslideOutLeft 0.3s ease;\r\n}\r\n\r\n.gslideInLeft {\r\n\t-webkit-animation: gslideInLeft 0.3s ease;\r\n\tanimation: gslideInLeft 0.3s ease;\r\n}\r\n\r\n.gslideOutRight {\r\n\t-webkit-animation: gslideOutRight 0.3s ease;\r\n\tanimation: gslideOutRight 0.3s ease;\r\n}\r\n\r\n.gslideInRight {\r\n\t-webkit-animation: gslideInRight 0.3s ease;\r\n\tanimation: gslideInRight 0.3s ease;\r\n}\r\n\r\n.gzoomIn {\r\n\t-webkit-animation: gzoomIn 0.5s ease;\r\n\tanimation: gzoomIn 0.5s ease;\r\n}\r\n\r\n.gzoomOut {\r\n\t-webkit-animation: gzoomOut 0.5s ease;\r\n\tanimation: gzoomOut 0.5s ease;\r\n}\r\n\r\n@-webkit-keyframes lightboxLoader {\r\n\t0% {\r\n\t\t-webkit-transform: rotate(0deg);\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\t100% {\r\n\t\t-webkit-transform: rotate(360deg);\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n@keyframes lightboxLoader {\r\n\t0% {\r\n\t\t-webkit-transform: rotate(0deg);\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\t100% {\r\n\t\t-webkit-transform: rotate(360deg);\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gfadeIn {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@keyframes gfadeIn {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gfadeOut {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t}\r\n\tto {\r\n\t\topacity: 0;\r\n\t}\r\n}\r\n\r\n@keyframes gfadeOut {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t}\r\n\tto {\r\n\t\topacity: 0;\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gslideInLeft {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(-60%, 0, 0);\r\n\t\ttransform: translate3d(-60%, 0, 0);\r\n\t}\r\n\tto {\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@keyframes gslideInLeft {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: translate3d(-60%, 0, 0);\r\n\t\ttransform: translate3d(-60%, 0, 0);\r\n\t}\r\n\tto {\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gslideOutLeft {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\tto {\r\n\t\t-webkit-transform: translate3d(-60%, 0, 0);\r\n\t\ttransform: translate3d(-60%, 0, 0);\r\n\t\topacity: 0;\r\n\t\tvisibility: hidden;\r\n\t}\r\n}\r\n\r\n@keyframes gslideOutLeft {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\tto {\r\n\t\t-webkit-transform: translate3d(-60%, 0, 0);\r\n\t\ttransform: translate3d(-60%, 0, 0);\r\n\t\topacity: 0;\r\n\t\tvisibility: hidden;\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gslideInRight {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(60%, 0, 0);\r\n\t\ttransform: translate3d(60%, 0, 0);\r\n\t}\r\n\tto {\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@keyframes gslideInRight {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(60%, 0, 0);\r\n\t\ttransform: translate3d(60%, 0, 0);\r\n\t}\r\n\tto {\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gslideOutRight {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\tto {\r\n\t\t-webkit-transform: translate3d(60%, 0, 0);\r\n\t\ttransform: translate3d(60%, 0, 0);\r\n\t\topacity: 0;\r\n\t}\r\n}\r\n\r\n@keyframes gslideOutRight {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t\tvisibility: visible;\r\n\t\t-webkit-transform: translate3d(0, 0, 0);\r\n\t\ttransform: translate3d(0, 0, 0);\r\n\t}\r\n\tto {\r\n\t\t-webkit-transform: translate3d(60%, 0, 0);\r\n\t\ttransform: translate3d(60%, 0, 0);\r\n\t\topacity: 0;\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gzoomIn {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: scale3d(0.3, 0.3, 0.3);\r\n\t\ttransform: scale3d(0.3, 0.3, 0.3);\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@keyframes gzoomIn {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: scale3d(0.3, 0.3, 0.3);\r\n\t\ttransform: scale3d(0.3, 0.3, 0.3);\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@-webkit-keyframes gzoomOut {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t}\r\n\t50% {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: scale3d(0.3, 0.3, 0.3);\r\n\t\ttransform: scale3d(0.3, 0.3, 0.3);\r\n\t}\r\n\tto {\r\n\t\topacity: 0;\r\n\t}\r\n}\r\n\r\n@keyframes gzoomOut {\r\n\tfrom {\r\n\t\topacity: 1;\r\n\t}\r\n\t50% {\r\n\t\topacity: 0;\r\n\t\t-webkit-transform: scale3d(0.3, 0.3, 0.3);\r\n\t\ttransform: scale3d(0.3, 0.3, 0.3);\r\n\t}\r\n\tto {\r\n\t\topacity: 0;\r\n\t}\r\n}\r\n\r\n//</editor-fold> -- CSS Animations --\r\n\r\n@media (min-width: 769px) {\r\n\t//<editor-fold desc=\"-- set up\t -- \">\r\n\t.glightbox-container .ginner-container {\r\n\t\twidth: auto;\r\n\t\theight: auto;\r\n\t\t-webkit-box-orient: horizontal;\r\n\t\t-webkit-box-direction: normal;\r\n\t\t-ms-flex-direction: row;\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.glightbox-container .ginner-container.desc-top .gslide-description {\r\n\t\t-webkit-box-ordinal-group: 1;\r\n\t\t-ms-flex-order: 0;\r\n\t\torder: 0;\r\n\t}\r\n\r\n\t.glightbox-container .ginner-container.desc-top .gslide-image,\r\n\t.glightbox-container .ginner-container.desc-top .gslide-image img {\r\n\t\t-webkit-box-ordinal-group: 2;\r\n\t\t-ms-flex-order: 1;\r\n\t\torder: 1;\r\n\t}\r\n\r\n\t.glightbox-container .ginner-container.desc-left .gslide-description {\r\n\t\t-webkit-box-ordinal-group: 1;\r\n\t\t-ms-flex-order: 0;\r\n\t\torder: 0;\r\n\t}\r\n\r\n\t.glightbox-container .ginner-container.desc-left .gslide-image {\r\n\t\t-webkit-box-ordinal-group: 2;\r\n\t\t-ms-flex-order: 1;\r\n\t\torder: 1;\r\n\t}\r\n\r\n\t.gslide-image img {\r\n\t\tmax-height: 97vh;\r\n\t\tmax-width: 100%;\r\n\t}\r\n\r\n\t.gslide-image img.zoomable {\r\n\t\t//cursor: -webkit-zoom-in;\r\n\t\tcursor: zoom-in;\r\n\t}\r\n\r\n\t.zoomed .gslide-image img.zoomable {\r\n\t\t//cursor: -webkit-grab;\r\n\t\tcursor: grab;\r\n\t}\r\n\r\n\t.gslide-inline {\r\n\t\tmax-height: 95vh;\r\n\t}\r\n\r\n\t.gslide-external {\r\n\t\tmax-height: 100vh;\r\n\t}\r\n\r\n\t.gslide-description.description-left,\r\n\t.gslide-description.description-right {\r\n\t\tmax-width: 275px;\r\n\t}\r\n\r\n\t.glightbox-open {\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t.goverlay {\r\n\t\tbackground: rgba(0, 0, 0, 0.92);\r\n\t}\r\n\r\n\t//</editor-fold> -- set up\t --\r\n\t.glightbox-clean {\r\n\t\t.gslide-media {\r\n\t\t\t-webkit-box-shadow: 1px 2px 9px 0px rgba(0, 0, 0, 0.65);\r\n\t\t\tbox-shadow: 1px 2px 9px 0px rgba(0, 0, 0, 0.65);\r\n\t\t}\r\n\r\n\t\t.description-left .gdesc-inner,\r\n\t\t.description-right .gdesc-inner {\r\n\t\t\tposition: absolute;\r\n\t\t\theight: 100%;\r\n\t\t\toverflow-y: auto;\r\n\t\t}\r\n\r\n\t\t.gprev,\r\n\t\t.gnext,\r\n\t\t.gclose {\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.32);\r\n\t\t}\r\n\r\n\t\t.gprev:hover,\r\n\t\t.gnext:hover,\r\n\t\t.gclose:hover {\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.7);\r\n\t\t}\r\n\r\n\t\t.gprev {\r\n\t\t\ttop: 45%;\r\n\t\t}\r\n\r\n\t\t.gnext {\r\n\t\t\ttop: 45%;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n@media (min-width: 992px) {\r\n\t.glightbox-clean .gclose {\r\n\t\topacity: 0.7;\r\n\t\tright: 20px;\r\n\t}\r\n}\r\n\r\n@media screen and (max-height: 420px) {\r\n\t.goverlay {\r\n\t\tbackground: #000;\r\n\t}\r\n}\r\n\r\n\r\n", "//\r\n//\r\ntable.CalendarListView {\r\n\tmax-width: 100%;\r\n\twidth: 100%; //Important!\r\n\r\n\r\n\ttbody {\r\n\t\tline-height: 1.2;\r\n\r\n\t\t& > tr {\r\n\t\t\tmax-width: 100%;\r\n\t\t\twidth: 100%; //Important!\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-template-columns: 12%  auto; /* Last column 'auto' takes remaining space */\r\n\t\t\t//\tgrid-template-columns: 1fr 2fr auto; /* Last column 'auto' takes remaining space */\r\n\t\t\tpadding: 15px 0;\r\n\t\t\tborder-bottom: 0 !important;\r\n\r\n\t\t\t@media screen and (max-width: ($atMedia_ipad_max-width)) {\r\n\t\t\t\tgrid-template-columns: 20%   auto;\r\n\t\t\t}\r\n\r\n\t\t\ttd.date, th.date {\r\n\t\t\t\tgrid-column: 1;\r\n\t\t\t}\r\n\r\n\t\t\ttd.gig, th.gig {\r\n\t\t\t\tgrid-column: 2;\r\n\t\t\t}\r\n\r\n\t\t\t//td.info, th.info { //\tgrid-column: 3;\t //\toverflow-wrap: anywhere; \t//}\r\n\t\t\t// paddings\r\n\t\t\ttd, th {\r\n\t\t\t\t@media screen and (max-width: ($atMedia_ipad_max-width)) {\r\n\t\t\t\t\tpadding: 5px;\r\n\t\t\t\t}\r\n\t\t\t\t@media screen and (max-width: (450px)) {\r\n\t\t\t\t\tfont-size: 13px;\r\n\t\t\t\t}\r\n\t\t\t\t@media screen and (max-width: (350px)) {\r\n\t\t\t\t\tfont-size: 12px;\r\n\t\t\t\t\tpadding: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// no, because i only want an imge to be flush.  if its text, i want the padding.\r\n\t\t\t// td:last-child {\r\n\t\t\t//\tpadding-right: 0;\r\n\t\t\t//}\r\n\r\n\t\t\ttd.gig {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\t@media screen and (max-width: ($atMedia_ipad_max-width)) {\r\n\t\t\t\t\tdisplay: table-cell;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tdiv.text {\r\n\t\t\t\t\tflex-grow: 1; /* This ensures that .text div take up the remaining space */\r\n\t\t\t\t\ttext-align: justify;\r\n\t\t\t\t\thyphens: auto;\r\n\r\n\t\t\t\t\ta {@include links(inherit, inherit, linkDecUnderlineImportant, linkDecUnderlineImportant);}\r\n\r\n\t\t\t\t\tdiv.otherfiles a {\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tdiv.loc {\r\n\t\t\t\t\t\tfont-style: normal;\r\n\r\n\t\t\t\t\t\ta {\r\n\t\t\t\t\t\t\tfont-style: italic;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.locationMain {\r\n\t\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t\t\tfont-style: normal;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.dashicons-location {\r\n\t\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\t\tleft: -5px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tdiv.title {\r\n\t\t\t\t\t\tfont-weight: normal;\r\n\t\t\t\t\t\tfont-style: normal;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tdiv:not(:first-child) {\r\n\t\t\t\t\t\tmargin-top: 10px;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tdiv.imgs {\r\n\t\t\t\t\t// dont need this anymore, cuz now its just a stack of thumbs\r\n\t\t\t\t\t//flex-basis: auto; /* This lets .files div take up only what space it needs */\r\n\t\t\t\t\t//display: flex;\r\n\t\t\t\t\t//flex-direction: column;\r\n\t\t\t\t\t//align-items: flex-end;\r\n\t\t\t\t\t//margin-left: auto; /* This automatically assigns all left over space on the left of the .files div */\r\n\t\t\t\t\t//min-width: 110px;\r\n\t\t\t\t\t//padding-left: 10px;\r\n\r\n\t\t\t\t\tmargin-left: 10px;\r\n\t\t\t\t\t// ech, idk if i like this...\r\n\t\t\t\t\t// left: 10px; //i want the imgs to be flush right but i dont want to take out the padding from the td\r\n\t\t\t\t\t//position: relative;\r\n\t\t\t\t\t@media screen and (max-width: ($atMedia_ipad_max-width)) {\r\n\t\t\t\t\t\tdisplay: flex; /* Flexbox layout */\r\n\t\t\t\t\t\tflex-direction: row; /* Align items in a row */\r\n\t\t\t\t\t\talign-items: flex-start; /* Align items at the top */\r\n\t\t\t\t\t\tmargin-left: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\ta.glightbox {\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\t@media screen and (max-width: ($atMedia_ipad_max-width)) {\r\n\t\t\t\t\t\t\tdisplay: inline-block;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t//min-width: 100px;\r\n\t\t\t\t\t\t/*white-space: nowrap;*/\r\n\t\t\t\t\t\t&:not(:first-child) {\r\n\t\t\t\t\t\t\tmargin-top: 10px;\r\n\t\t\t\t\t\t\t@media screen and (max-width: ($atMedia_ipad_max-width)) {\r\n\t\t\t\t\t\t\t\tmargin-top: 0;\r\n\t\t\t\t\t\t\t\tmargin-left: 10px;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\timg {\r\n\t\t\t\t\t\t\twidth: 100px;\r\n\t\t\t\t\t\t\tmin-width: 100px; //idk wtf it has to be both of these.\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\t}\r\n}\r\n", "//\r\n#headers_container {\r\n\tborder-bottom: 1px solid #2f2e2e;\r\n\tposition: sticky;\r\n\ttop: 0;\r\n\tz-index: 9999999;\r\n\r\n\t#mainheader_container {\r\n\t\t#mainheader_wrapper {\r\n\t\t\t#logo-and-text {\r\n\t\t\t\tpadding: 3px 0;\r\n\r\n\t\t\t\t#logo {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t#text {\r\n\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t//#site-title-text {\r\n\t\t\t\t\t//\ta {\r\n\t\t\t\t\t//\t\t@include links($linkcolorDkBg, $linkoverDkBg !important, linkDecNo, linkDecNo); // use important in quotes 'linkDecUnderline !important'\r\n\t\t\t\t\t//\t\tcolor: $fontColorDkBg;\r\n\t\t\t\t\t//\t\ttext-decoration: none !important;\r\n\t\t\t\t\t//\t\tfont-family: $font_header;\r\n\t\t\t\t\t//\t\ttext-shadow: 1px 1px 1px #000;\r\n\t\t\t\t\t//\t\tfont-size: 30px;\r\n\t\t\t\t\t//\t\tfont-weight: 900;\r\n\t\t\t\t\t//\t\ttext-transform: uppercase;\r\n\t\t\t\t\t//\t}\r\n\t\t\t\t\t//}\r\n\r\n\t\t\t\t\t#site-description-text {\r\n\t\t\t\t\t\tdisplay: none;\r\n\t\t\t\t\t\t//font-size: 18px;\r\n\t\t\t\t\t\t//opacity: .8;\r\n\t\t\t\t\t\t//color: $white;\r\n\t\t\t\t\t\t//text-transform: uppercase;\r\n\t\t\t\t\t\t//margin: -6px 0 0 0;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n\r\n\r\n", "//\r\n//\r\n.divider {\r\n\t@include wrapper;\r\n\twidth: 100%;\r\n\theight: 1px;\r\n\tbackground-color: #e5e5e5;\r\n\tmargin: 4vw auto;\r\n}\r\n\r\n\r\n// portfolio lightbox content\r\n/*\r\n<div id=\"<?=$lightbox_id?>\" class=\"glightbox-content\" style=\"display:none\">\r\n\t\t\t\t\t\t<div class=\"lightbox-content\">\r\n\t\t\t\t\t\t\t <div class=\"lightbox-image\">\r\n\t\t\t\t\t\t\t\t\t<img src=\"<?=esc_url($card['img_url'])?>\" alt=\"<?=esc_attr($card['title'])?>\">\r\n\t\t\t\t\t\t\t </div>\r\n\r\n\t\t\t\t\t\t\t <div class=\"lightbox-text\">\r\n\t\t\t\t\t\t\t\t<h3><?=$card['title']?></h3>\r\n\t\t\t\t\t\t\t\t<div class=\"subtitle\"><?=$card['subtitle']?></div>\r\n\t\t\t\t\t\t\t\t<div class=\"content\"><?=$card['content']?></div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n*/\r\n.glightbox-content {\r\n\tdisplay: none;\r\n\tpadding: 6vw;\r\n\r\n\t.lightbox-content {\r\n\t\tdisplay: grid;\r\n\t\tgap: 2rem;\r\n\t\tgrid-template-columns: 1fr 2fr;\r\n\t\talign-items: start;\r\n\r\n\t\t.lightbox-text {\r\n\t\t\torder: 1;\r\n\t\t\toverflow-y: auto;\r\n\t\t\tmax-height: 80vh;\r\n\t\t\tpadding-right: 1.5rem;\r\n\t\t}\r\n\r\n\t\t.lightbox-image {\r\n\t\t\torder: 2;\r\n\r\n\t\t\timg {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: auto;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\r\n\t\t// Mobile styles\r\n\t\t@include up_to_ipad {\r\n\t\t\tgrid-template-columns: 1fr;\r\n\t\t\tgrid-template-rows: auto auto;\r\n\r\n\t\t\t.lightbox-image {\r\n\t\t\t\torder: 1;\r\n\r\n\t\t\t\timg {\r\n\t\t\t\t\tmax-height: 50vh;\r\n\t\t\t\t\twidth: auto;\r\n\t\t\t\t\tmargin: 0 auto;\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t.lightbox-text {\r\n\t\t\t\torder: 2;\r\n\t\t\t\tmax-height: none;\r\n\t\t\t\toverflow-y: visible;\r\n\t\t\t\tpadding-right: 0;\r\n\t\t\t\tpadding-top: 1.5rem;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n\r\n.domain-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(2, 1fr);\r\n\tgap: 1.5vw;\r\n\tmargin-bottom: 0;\r\n\twidth: 50%;\r\n\t@include up_to_ipad {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.domain-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 1vw;\r\n\t\tborder: 1px solid #ddd;\r\n\t\tborder-radius: 5px;\r\n\r\n\t\t> div:first-child {\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #333;\r\n\t\t}\r\n\r\n\t\t> div:last-child {\r\n\t\t\tcolor: #2c3e50;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.examplecosts {\r\n\twidth: 33%;\r\n\t@include up_to_ipad {\r\n\t\twidth: 100%;\r\n\t}\r\n}\r\n\r\n\r\n.d_createGallery .d_createGallery__item {\r\n\t@include shadowtwo();\r\n\tborder-radius: 3px;\r\n\toverflow: hidden;\r\n}\r\n\r\n#contact.container {\r\n\t@include container;\r\n\r\n\t.wrapper {\r\n\t\t@include wrapper;\r\n\r\n\t\t.card {\r\n\t\t\tmax-width: 500px;\r\n\t\t\t@include shadowtwo();\r\n\t\t\t@include responsive-border-radius;\r\n\t\t\toverflow: hidden;\r\n\t\t\t@include responsive-padding-01_5-02_foursides;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.pageanchor {\r\n\tscroll-margin-top: 110px; /* adjust this to your header height */\r\n}\r\n", "\r\nh1, .h1, h2, .h2 {\r\n\ttext-transform: uppercase !important;\r\n}\r\n\r\n\r\n\r\n\r\n", "\r\nbody.is_front_page {\r\n\t//#headers_container #mainheader_container #mainheader_wrapper\r\n\t#headers_container #mainheader_container #mainheader_wrapper {\r\n\t\t#logo-and-text {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\r\n\t\t#menu {\r\n\t\t\tmargin: 1vw 0 !important;\r\n\t\t}\r\n\t}\r\n\r\n\t#body_container {\r\n\t\t@media screen and (max-width: (550px)) {\r\n\t\t\t#fb_col { // this is to give you room to scroll, with your finger. otherwise, you'd just be scrolling the fb iframe, forever.  FOR EVERRR\r\n\t\t\t\tmargin-right: 25px;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t$hero-height: max-content;\r\n\t$hero-split-height: 10vw;\r\n\t//<div id=\"hero\">\r\n\t//<div id=\"herotop\">\r\n\t//\t\t<div id=\"herowrapper\">\r\n\t//<div id=\"herobottom\">\r\n\t#hero {\r\n\t\tposition: relative;\r\n\t\tz-index: 3;\r\n\t\tbackground-image: linear-gradient(to top, transparent $hero-split-height, $dkblue $hero-split-height, $dkpurple);\r\n\t\t//@include up_to_wrapper {\r\n\t\t//\tpadding-left: $wrapper_padding;\r\n\t\t//\tpadding-right: $wrapper_padding;\r\n\t\t//}\r\n\t\t//display: grid;\r\n\t\t//grid-template-columns: 1fr 1fr;\r\n\t\t//gap: 3vw 8vw;\r\n\t\t//grid-template-rows: max-content max-content;\r\n\t\t//align-content: center;\r\n\t\t//align-items: center;\r\n\t\t//justify-content: space-between;\r\n\r\n\t\t#herotop.container {\r\n\t\t\t@include container;\r\n\r\n\t\t\t#herowrapper.wrapper {\r\n\t\t\t\t@include wrapper;\r\n\t\t\t\theight: $hero-height;\r\n\t\t\t\ttext-align: left;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tgap: $wrapper_padding;\r\n\r\n\t\t\t\tpadding-top: 4vw; // this is override\r\n\t\t\t\tpadding-bottom: 6vw; // this is override\r\n\t\t\t\t@include up_to_wrapper {\r\n\t\t\t\t\tpadding-top: 4vw;\r\n\t\t\t\t\tpadding-bottom: 6vw;\r\n\t\t\t\t}\r\n\t\t\t\t@include up_to_ipad {\r\n\t\t\t\t\tpadding-top: 4vw;\r\n\t\t\t\t\tpadding-bottom: 6vw;\r\n\t\t\t\t}\r\n\t\t\t\t@include up_to_small {\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t}\r\n\r\n\r\n\t\t\t\t#title {\r\n\t\t\t\t\th1 {\r\n\t\t\t\t\t\tcolor: #fff;\r\n\t\t\t\t\t\t//font-size: 9rem;\r\n\t\t\t\t\t\t@include fluid-type-clamp(40px, 100px);\r\n\t\t\t\t\t\tmargin: 0;\r\n\t\t\t\t\t\tline-height: .9;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t#doug {\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t#cassidy {\r\n\t\t\t\t\t\tcolor: $ltblue;\r\n\t\t\t\t\t\tleft: -11px; // this just looked a little wierd.\r\n\t\t\t\t\t\t@include up_to_ipad {\r\n\t\t\t\t\t\t\tleft: -4px;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tposition: relative;\r\n\t\t\t\t\t\tmargin-bottom: 6vw;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tbutton, input[type=\"button\"] {\r\n\t\t\t\t\t\tpadding: .5vw 1vw;\r\n\t\t\t\t\t\tborder-radius: 1vw;\r\n\t\t\t\t\t\tborder: 2px solid $white;\r\n\t\t\t\t\t\tbackground: $dkpurple;\r\n\t\t\t\t\t\tcolor: $white;\r\n\t\t\t\t\t\t@include fluid-type-clamp(14px, 18px);\r\n\t\t\t\t\t\tfont-weight: bold;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t#blurb {\r\n\t\t\t\t\tmargin-bottom: $wrapper_padding;\r\n\r\n\t\t\t\t\tp {\r\n\t\t\t\t\t\tcolor: $fontColorDkBg;\r\n\t\t\t\t\t\t@include fluid-type-clamp(15px, 18px);\r\n\t\t\t\t\t\ttext-align: right;\r\n\t\t\t\t\t\t@include up_to_small {\r\n\t\t\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\t\t\tmargin-top: 4vw;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t}\r\n\r\n\r\n\t\t#herobottom {\r\n\t\t\tz-index: 1;\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: $hero-split-height;\r\n\t\t\ttransform: rotate(0) scaleX(-1);\r\n\t\t\tz-index: -1;\r\n\t\t\tdisplay: block;\r\n\r\n\t\t\tsvg {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 100%;\r\n\t\t\t\tdisplay: block;\r\n\t\t\t\tmargin: -1px 0;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t#home2.container {\r\n\t\t@include container;\r\n\t\tmargin-top: 4vw;\r\n\t\tmargin-bottom: 4vw;\r\n\r\n\t\t.wrapper {\r\n\t\t\tdisplay: grid;\r\n\t\t\tgrid-template-columns: 60fr 40fr;\r\n\t\t\tgap: $wrapper_padding;\r\n\t\t\t@include wrapper;\r\n\r\n\t\t\t.blurb { }\r\n\r\n\t\t\t.img {\r\n\r\n\t\t\t\timg#dugbeanbag {\r\n\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t//width: 30vw;\r\n\t\t\t\t\t//max-width: 400px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t}\r\n\t}\r\n}\r\n// end body.is_front_page\r\n.wavy-divider {\r\n\tposition: relative;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\tz-index: -2;\r\n\tmargin: ($wrapper_padding / 2) auto 0;\r\n}\r\n\r\n.wavy-divider .svg-container {\r\n\tposition: relative;\r\n\theight: 6rem;\r\n}\r\n\r\n.wavy-divider .svg-container svg {\r\n\theight: 100%;\r\n\twidth: 100%;\r\n\tposition: absolute;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n}\r\n\r\n", "//\r\nbody.is_front_page #wprmenu_bar.d_mod #d_wprm_bar_logo .bar_logo {\r\n\topacity: 0;\r\n\tvisibility: hidden;\r\n}\r\n \r\n#wprmenu_bar.d_mod {\r\n\t//d_wprm_menu_title\r\n\t//d_wprm_bar_logo\r\n\t//d_wprm_hamburger\r\n\r\n\t#d_wprm_bar_logo {\r\n\r\n\r\n\t\t.bar_logo {\r\n\t\t\theight: 48px;\r\n\t\t\ttop: 3px;\r\n\t\t}\r\n\t}\r\n\r\n\tdiv#d_wprm_menu_title {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t//div#d_wprm_menu_title {\r\n\t//\tspan.wpr_title {\r\n\t//\t\tdisplay: flex;\r\n\t//\r\n\t//\t\talign-items: baseline; /* 👈 Align both on text baseline */\r\n\t//\r\n\t//\t\t.title {\r\n\t//\t\t\tfont-weight: bold !important;\r\n\t//\t\t\tcolor: $H1_color;\r\n\t//\t\t}\r\n\t//\r\n\t//\t\t.description {\r\n\t//\t\t\ttext-transform: capitalize;\r\n\t//\t\t\tfont-size: 14px;\r\n\t//\t\t\tmargin-left: $wrapper_padding;\r\n\t//\t\t}\r\n\t//\r\n\t//\t}\r\n\t//\r\n\t//}\r\n\r\n\r\n}\r\n", "\r\n#services.cardscontainer .cardsgrid {\r\n\t@include gridColumns4431;\r\n}\r\n\r\n#portfolio.cardscontainer {\r\n\t.textwrap {\r\n\t\t.header {\r\n\t\t\tmargin-bottom: 1vw;\r\n\r\n\t\t\th2 {\r\n\t\t\t\tmargin-bottom: 0;\r\n\t\t\t\t// fluid padding based on container: padding 6vw 0 at 250px going down to padding 1vw 0 at 400px\r\n\t\t\t\t// Define your padding values for each step\r\n\t\t\t\t$padding-steps: (5% 0, 4% 0, 3% 0, 2% 0);\r\n\t\t\t\t@include fluid-container-padding($padding-steps, 225px, $small_max-width);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.blurb, .footer {\r\n\t\t\tdisplay: none;\r\n\r\n\t\t\t&::after {\r\n\t\t\t\tcontent: 'poop';\r\n\t\t\t\tdisplay: block; // Make it visible\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n}\r\n\r\n"], "file": "style.debug.css"}