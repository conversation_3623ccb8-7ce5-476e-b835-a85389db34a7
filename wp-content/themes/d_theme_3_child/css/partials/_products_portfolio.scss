
#services.cardscontainer .cardsgrid {
	@include gridColumns4431;
}

#portfolio.cardscontainer {
	.textwrap {
		.header {
			margin-bottom: 1vw;

			h2 {
				margin-bottom: 0;
				// fluid padding based on container: padding 6vw 0 at 250px going down to padding 1vw 0 at 400px
				// Define your padding values for each step
				$padding-steps: (5% 0, 4% 0, 3% 0, 2% 0);
				@include fluid-container-padding($padding-steps, 225px, $small_max-width);
			}
		}

		.blurb, .footer {
			display: none;

			&::after {
				content: 'poop';
				display: block; // Make it visible
			}
		}

	}
}

