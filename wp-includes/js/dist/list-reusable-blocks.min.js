/*! This file is auto-generated */
(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var o in n)e.o(n,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:n[o]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.wp.element,n=window.wp.i18n;var o=function(){return o=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},o.apply(this,arguments)};Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;function r(e){return e.toLowerCase()}var s=[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g],a=/[^A-Z0-9]+/gi;function i(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}function l(e,t){return void 0===t&&(t={}),function(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,o=void 0===n?s:n,l=t.stripRegexp,c=void 0===l?a:l,p=t.transform,d=void 0===p?r:p,u=t.delimiter,w=void 0===u?" ":u,f=i(i(e,o,"$1\0$2"),c,"\0"),m=0,b=f.length;"\0"===f.charAt(m);)m++;for(;"\0"===f.charAt(b-1);)b--;return f.slice(m,b).split("\0").map(d).join(w)}(e,o({delimiter:"."},t))}const c=window.wp.apiFetch;var p=e.n(c);const d=window.wp.blob;const u=async function(e){const t=await p()({path:"/wp/v2/types/wp_block"}),n=await p()({path:`/wp/v2/${t.rest_base}/${e}?context=edit`}),r=n.title.raw,s=n.content.raw,a=n.wp_pattern_sync_status,i=JSON.stringify({__file:"wp_block",title:r,content:s,syncStatus:a},null,2),c=(void 0===u&&(u={}),l(r,o({delimiter:"-"},u))+".json");var u;(0,d.downloadBlob)(c,i,"application/json")},w=window.wp.compose,f=window.wp.components;const m=async function(e){const t=await function(e){const t=new window.FileReader;return new Promise((n=>{t.onload=()=>{n(t.result)},t.readAsText(e)}))}(e);let n;try{n=JSON.parse(t)}catch(e){throw new Error("Invalid JSON file")}if("wp_block"!==n.__file||!n.title||!n.content||"string"!=typeof n.title||"string"!=typeof n.content||n.syncStatus&&"string"!=typeof n.syncStatus)throw new Error("Invalid pattern JSON file");const o=await p()({path:"/wp/v2/types/wp_block"});return await p()({path:`/wp/v2/${o.rest_base}`,data:{title:n.title,content:n.content,status:"publish",meta:"unsynced"===n.syncStatus?{wp_pattern_sync_status:n.syncStatus}:void 0},method:"POST"})},b=window.ReactJSXRuntime;const _=(0,w.withInstanceId)((function({instanceId:e,onUpload:o}){const r="list-reusable-blocks-import-form-"+e,s=(0,t.useRef)(),[a,i]=(0,t.useState)(!1),[l,c]=(0,t.useState)(null),[p,d]=(0,t.useState)(null);return(0,b.jsxs)("form",{className:"list-reusable-blocks-import-form",onSubmit:e=>{e.preventDefault(),p&&(i({isLoading:!0}),m(p).then((e=>{s&&(i(!1),o(e))})).catch((e=>{if(!s)return;let t;switch(e.message){case"Invalid JSON file":t=(0,n.__)("Invalid JSON file");break;case"Invalid pattern JSON file":t=(0,n.__)("Invalid pattern JSON file");break;default:t=(0,n.__)("Unknown error")}i(!1),c(t)})))},ref:s,children:[l&&(0,b.jsx)(f.Notice,{status:"error",onRemove:()=>{c(null)},children:l}),(0,b.jsx)("label",{htmlFor:r,className:"list-reusable-blocks-import-form__label",children:(0,n.__)("File")}),(0,b.jsx)("input",{id:r,type:"file",onChange:e=>{d(e.target.files[0]),c(null)}}),(0,b.jsx)(f.Button,{__next40pxDefaultSize:!0,type:"submit",isBusy:a,accessibleWhenDisabled:!0,disabled:!p||a,variant:"secondary",className:"list-reusable-blocks-import-form__button",children:(0,n._x)("Import","button label")})]})}));const v=function({onUpload:e}){return(0,b.jsx)(f.Dropdown,{popoverProps:{placement:"bottom-start"},contentClassName:"list-reusable-blocks-import-dropdown__content",renderToggle:({isOpen:e,onToggle:t})=>(0,b.jsx)(f.Button,{size:"compact",className:"list-reusable-blocks-import-dropdown__button","aria-expanded":e,onClick:t,variant:"primary",children:(0,n.__)("Import from JSON")}),renderContent:({onClose:t})=>(0,b.jsx)(_,{onUpload:(0,w.pipe)(t,e)})})};document.body.addEventListener("click",(e=>{e.target.classList.contains("wp-list-reusable-blocks__export")&&(e.preventDefault(),u(e.target.dataset.id))})),document.addEventListener("DOMContentLoaded",(()=>{const e=document.querySelector(".page-title-action");if(!e)return;const o=document.createElement("div");o.className="list-reusable-blocks__container",e.parentNode.insertBefore(o,e),(0,t.createRoot)(o).render((0,b.jsx)(t.StrictMode,{children:(0,b.jsx)(v,{onUpload:()=>{const e=document.createElement("div");e.className="notice notice-success is-dismissible",e.innerHTML=`<p>${(0,n.__)("Pattern imported successfully!")}</p>`;const t=document.querySelector(".wp-header-end");t&&t.parentNode.insertBefore(e,t)}})}))})),(window.wp=window.wp||{}).listReusableBlocks={}})();