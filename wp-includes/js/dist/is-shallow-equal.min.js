/*! This file is auto-generated */
(()=>{"use strict";var r={d:(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o:(r,e)=>Object.prototype.hasOwnProperty.call(r,e),r:r=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})}},e={};function t(r,e){if(r===e)return!0;const t=Object.keys(r),n=Object.keys(e);if(t.length!==n.length)return!1;let o=0;for(;o<t.length;){const n=t[o],i=r[n];if(void 0===i&&!e.hasOwnProperty(n)||i!==e[n])return!1;o++}return!0}function n(r,e){if(r===e)return!0;if(r.length!==e.length)return!1;for(let t=0,n=r.length;t<n;t++)if(r[t]!==e[t])return!1;return!0}function o(r,e){if(r&&e){if(r.constructor===Object&&e.constructor===Object)return t(r,e);if(Array.isArray(r)&&Array.isArray(e))return n(r,e)}return r===e}r.r(e),r.d(e,{default:()=>o,isShallowEqualArrays:()=>n,isShallowEqualObjects:()=>t}),(window.wp=window.wp||{}).isShallowEqual=e})();