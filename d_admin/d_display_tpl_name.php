<?php

add_action('wp_before_admin_bar_render', function(){   // puts the tpl in the admin bar (if showing, at top)  not the footer
	if( !is_admin() && is_admin_bar_showing()){
		global $wp_admin_bar;
		$tpl = endOfPath($GLOBALS['current_theme_template'], 2);
		$wp_admin_bar->add_menu(['parent' => false, // use 'false' for a root menu, or pass the ID of the parent menu
								 'id'     => 'displayTemplateName', // link ID, defaults to a sanitized title value
								 'title'  => 'tpl: <b>'.$tpl.'</b>', // link title
								 'meta'   => ['title' => $templateInfos] // array of any of the following options: array( 'html' => '', 'class' => '', 'onclick' => '', target => '', title => '' );
		]);
	}
});


add_filter('template_include', function($t){
	$GLOBALS['current_theme_template'] = $t;// Retrieve the current template name et save into a global variable current_theme_template */
	return $t;
}, 1000);//this is the main thing


function d_display_tpl_wp_footer(){
	if(defined('noAdminbuttons') and constant('noAdminbuttons') == 1){
		return;
	}//new

	if(is_dev() and isAdministrator()){
		$tpl = endOfPath($GLOBALS['current_theme_template'], 2);
		echo div('class="d_admin_footer"');
		echo wdiv(get_num_queries().'  queries. Entire script execution: '.timer_stop(0).' seconds.', 'id="querynum" class="clearfix alignright"');// querys and timer
		echo wdiv('tpl: '.$tpl, 'id="current_theme_template"  ');                                                                                  //current tpl
		echo div(0);
	}
}

add_action('wp_footer', 'd_display_tpl_wp_footer', 99999);
add_action('admin_footer', 'd_display_tpl_wp_footer', 99999);



